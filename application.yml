server:
  port: 30080
  servlet:
    context-path:

  # 错误页
  error:
    path: /error
    include-binding-errors: always
    include-exception: true
    include-message: always
    include-stacktrace: always

spring:
  application:
    name: 汕头市社会治理综合平台（内测版）

  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration

  datasource:
    main:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
#      jdbc-url: jdbc:mysql://**************:3306/health_code_dev?allowMultiQueries=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&useUnicode=TRUE&zeroDateTimeBehavior=CONVERT_TO_NULL
#      username: gmccai
#      password: LPDY!iLrUd8irpGp
      jdbc-url: *****************************************************************************************************************************************************************
      username: health_code_user
      password: pLu9i2zq1r&98K1K
      maximum-pool-size: 16
      minimum-pool-size: 16
      properties:
        hibernate:
          allow_update_outside_transaction: true
          cache:
            use_query_cache: false
            use_second_level_cache: false
          default_schema:
          dialect: com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect.MySQLDialect
          hbm2ddl:
            auto: update
          show_sql: true
      packages: com.chinamobile.sparrow.domain.model, com.chinamobile.healthcode.model

    log:
      config: shardingsphere.yml
      properties:
        hibernate:
          allow_update_outside_transaction: true
          cache:
            use_query_cache: false
            use_second_level_cache: false
          default_schema:
          dialect: com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect.MySQLInnoDBDialect
          hbm2ddl:
            auto: update
          show_sql: true

    vaccine:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://**************:3306/st_swzfw_fyxcx?allowMultiQueries=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&useUnicode=TRUE&zeroDateTimeBehavior=CONVERT_TO_NULL
      username: gmccai
      password: LPDY!iLrUd8irpGp
      maximum-pool-size: 16
      minimum-pool-size: 16
      properties:
        hibernate:
          allow_update_outside_transaction: true
          cache:
            use_query_cache: false
            use_second_level_cache: false
          default_schema:
          dialect: com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect.MySQLDialect
          hbm2ddl:
            auto: none
          show_sql: true

    health-commission:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://**************:3306/st_swzfw_fyxcx?allowMultiQueries=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&useUnicode=TRUE&zeroDateTimeBehavior=CONVERT_TO_NULL
      username: gmccai
      password: LPDY!iLrUd8irpGp
      maximum-pool-size: 16
      minimum-pool-size: 16
      properties:
        hibernate:
          allow_update_outside_transaction: true
          cache:
            use_query_cache: false
            use_second_level_cache: false
          default_schema:
          dialect: com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect.MySQLDialect
          hbm2ddl:
            auto: none
          show_sql: true

    fr:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      #jdbc-url: ********************************************************************************************************************************************************************
      #username: root
      #password: Gmcc@123456
      jdbc-url: jdbc:mysql://**************:3306/fr_mysql?allowMultiQueries=true&serverTimezone=GMT%2B8&characterEncoding=UTF-8&useUnicode=TRUE
      username: gmccai
      password: LPDY!iLrUd8irpGp
      maximum-pool-size: 16
      minimum-pool-size: 16
      properties:
        hibernate:
          allow_update_outside_transaction: true
          cache:
            use_query_cache: false
            use_second_level_cache: false
          default_schema:
          dialect: com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect.MySQLDialect
          hbm2ddl:
            auto: none
          show_sql: true

    quartz:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
#      jdbc-url: jdbc:mysql://**************:3306/health_code_dev?allowMultiQueries=true&serverTimezone=GMT%2B8&characterEncoding=UTF-8&useUnicode=TRUE
#      username: gmccai
#      password: LPDY!iLrUd8irpGp
      jdbc-url: ****************************************************************************************************************************
      username: health_code_user
      password: pLu9i2zq1r&98K1K
      maximum-pool-size: 2

  freemarker:
    enabled: true
    charset: UTF-8
    content-type: text/html
    expose-request-attributes: false
    expose-session-attributes: false
    prefer-file-system-access: false
    request-context-attribute: request
    suffix: .htm
    template-loader-path: classpath:/templates/

  gson:
    date-format: yyyy-MM-dd HH:mm:ss
    disable-html-escaping: true
    serialize-nulls: true

  jmx:
    enabled: false

  messages:
    basename: i18n/messages
    encoding: UTF-8

  mvc:
    converters:
      preferred-json-mapper: gson

  pid:
    file: app.pid

  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    wait-for-jobs-to-complete-on-shutdown: true

  redis:
#    host: ************
#    port: 56379
#    password: FwGMY00wW0iDiV
#    host: **************
    host: 127.0.0.1
    port: 6379
    password: redis
    lettuce:
      pool:
        max-active: 64

  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

management:
  server:
    port: 30081
  endpoint:
    health:
      show-details: always
    shutdown:
      enabled: true
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health, health-path, metrics, metrics-requireMetricName, self

# 日志
logging:
  config: logback-spring.xml

shiro:
  enabled: true
  web:
    enabled: true
  session-id-cookie:
    name: health-code-session-id
    http-only: false
    same-site: None
    secure: false
  remember-me-cookie:
    name: health-code-remember-me
    http-only: false
    same-site: None
    secure: false
    max-age: 2592000
  session:
    timeout: 1800
    validation-interval: 3600
  filter-chain-definition:
    - /sec/login/cancel, user
    - /sec/login/**, anon
    - /error, anon
    - /app/layout/header, anon
    - /app/init, anon
    - /util/cmpassport/app-id, anon
    - /citizen/profile/statistic/population, anon
    - /citizen/profile/statistic/crowds, anon
    - /subject/place/options/*, anon
    - /media/read, anon
    - /media/preview, anon
    - /media/playback, anon
    - /sec/crypto/rsa/public-key, anon
    - /sys/dept/get, anon
    - /emergency/alarm/conn, anon
    - /iot/andmu/message/receive, anon

    # 帆软报表
    - /monitoring-screen/**, fine-report

    # 静态资源
    - /favicon.ico, anon
    - /css/**, anon
    - /img/**, anon
    - /js/**, anon
    - /view/**, anon

    - /emergency/alarm/mock, anon

    # 其它
    - /**, user
  redis-session-dao:
    enabled: true
    key-template: "health-code:shiro:session:%s"
  redis-key-template:
    role: "health-code:authorization:user:%s:roles"
    permission: "health-code:authorization:user:%s:permissions"

# 切面
aspect:
  log:
    enabled: true
    max-length: 1000000

# 文件系统存储
file:
  dir:
    env:
    default:
    alarm: alarm
  extension:
    allowed: txt,doc,docx,xls,xlsx,ppt,pptx,pdf,ofd,bmp,jpg,jpeg,png,zip,rar
    forbidden:
  part:
    size: 262144

# okhttp连接池
okhttp:
  connection-pool:
    default:
      keep-alive: 5
      max-idle: 5

# 对象存储
s3:
  enabled: false
  endpoint: http://**************:9000
  access-key:
  secret-key:
  bucket:
    env: health-code-dev
    default: default

# 安全
sec:
  username:
  password-constraint: ^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$
  login-url: /sec/login
  captcha:
    enabled: false
    redis-key-template: health-code:client:captcha:%s
    redis-expires-in: 10
  jwt:
    expires-in: 2592000
  oauth2:
    client-id:
    client-secret:
  rsa:
    default:
      public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwiByu6C74TYTenPSdVP2jdHjgSZZuab16QUWyiseYsXq2FSt+eDwOr9eY5yuv80vmCou0diaL3h7UGUhZdzgDVLRFL2qUwbrvrmdBmkmWHCCpndQqcTLeQM+29wzWT/Nk9qWiePneLKpIXHtEm60Lx4/UTL88KBaAHb7I4s8swwIDAQAB
      private-key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALCIHK7oLvhNhN6c9J1U/aN0eOBJlm5pvXpBRbKKx5ixerYVK354PA6v15jnK6/zS+YKi7R2JoveHtQZSFl3OANUtEUvapTBuu+uZ0GaSZYcIKmd1CpxMt5Az7b3DNZP82T2paJ4+d4sqkhce0SbrQvHj9RMvzwoFoAdvsjizyzDAgMBAAECgYBorT085ca8S9Mm4bEW/gesfesTK+98p0zIip3Wgf5a55qZD7KLuqX6G4DfVOyN19nAna47ove2Zk6nfBpvmobmTpne1mvnIJw68bsBZ0KRcjVyq10EWRVdmPNXm0WjXLxijE1WUBE7hF4DfLacVceOKYM3cFYAKk+ZRPza2zqJyQJBANb5yQSA1b0kBkCt973ZV151L+sC2yXx4Lv7c97Yg3ZtZeFzTCGIC5zKnCsUCkKzkE7KYkUsg6yyW06Tc3SXmC8CQQDSODgkcG6nE5CihglIAdvh7bbdjaaNajKeiMclKRnOLLg6lPgVdAaHCUmPFadRFwV7LFmxALktoFgHyek3PbutAkBDoNbdJM6YruGMrW3Xeq0imQkXDJH2kO4bFRKxkzlgIzA+yW+0yMCmdZLFdot3yldmggKCMmvGA2H4CSj0D9CfAkB/y7+Nn3AVT9aQhs1jogWrDlkpzPAgbhwBtoLiO189sH/61Q8P8634L5QxUOeba1RgsfCAo0Dv1NdPPNbzRystAkEAinG+oELF+bjgReMiL9Wl8JrDjjJvO2xWuvSurtwRj0nlNTnET5VOH8ihX9eNjp0dyaZYCCcuKCuM3co6wYuwIQ==
  sha256:
    default:
      secret: 19890306
  sms:
    code-length: 6
    expires-in: 1
    template: d192367bb1894dd6ba4f43350da5e6dc

thread-pool:
  # 默认线程池
  default:
    core: 8
    max: 16

web:
  # 跨域
  cors:
    allow-credentials: true
    allowed-headers:
      - "*"
    allowed-methods:
      - GET
      - POST
      - OPTIONS
    allowed-origins:
      - http://localhost
      - http://localhost:5173
    exposed-headers:
      - "Content-Disposition"
    max-age: 3600

# 专项工作
project:
  # 待办短信模板
  sms-template-id: "3b2efdbd3cb8410e96f57d646b2dea62"
  remind-in-advance: 24

# 应急告警
alarm:
  sms-template-id: "fa303a2fd5714541beebd8b477cfb8d6"

citizen:
  qr-code-content-template: https://jfm2-api.shantou.gov.cn:50443/citizen/%s
  qr-code-logo-path: classpath:static/img/jinfengma.png
  profile:
    validation:
      threads: 4
      size: 1000
  grid:
    draw-url: "http://localhost/board/virtual-view.html"
  statistic:
    # 70岁以上老人，残疾人，婴儿，低保对象，精神病人
    crowds: "0, 2, 3, 7, 15"

migrant:
  redis-key:
    tip: health-code:migrant:tip
    policies: health-code:migrant:policies

truck:
  exp: 2
  qr-code-template: https://jfm2-api.shantou.gov.cn:50443/truck/ticket/scan?id=%s
  redis-key-template:
    ticket: health-code:ticket:%s
    passengers: health-code:ticket:%s:passengers

# 统一监控平台（视频云）
acs:
  base-url: https://************:10002/%s/v1/api/%s

# 和目
andmu:
  base-url: https://open.andmu.cn
  subscribe:
    camera: "ai, face_recognition"
    smoke-detector: "fireFighting_alarm, fireFighting_danger"

# 和对讲
poc:
  base-url: https://************:4482/station/mobile/serverapi.action

# 和对讲-视频版
poc-pro:
  base-url: https://*************:38888/web/api/v1

# 无人机
uav:
  base-url: http://***************:9090

# 高德地图
amap:
  api-key: f4a8e01b56d20027ae407b3c0b3a7a9c
  js-key: 02b3277d527b250c5592ea371658d220

# 中国移动手机号码认证
cmpassport:
  enabled: false
  app-id:
  app-key:
  rsa:
    public-key:
    private-key:
  token-url: https://token.cmpassport.com:8300/uniapi/uniTokenValidate

# 移动云MAS
mas:
  enabled: true
  sms:
    send-url: https://112.35.10.201:28888/sms/submit
    send-template-url: https://112.35.10.201:28888/sms/tmpsubmit
    ec-name: 汕头移动集团产品专用自用集团（内部应用）
    ap-id: st_f4j
    secret-key: 1qaz@WSX
    sign: 0ludzgmwk

# 社情地图
monitoring-screen:
  url: https://jfm-new.shantou.gov.cn/sqdt-internet/decision/view/form?viewlet=汕头社情地图public.frm
  title: 社情地图

# 企信通
qxt:
  enabled: false
  eid:
  userId:
  password:
  encrypt-key:
  port:
    default:
  receive-url: http://*************/SmsHttpInterface/smsService/Do-receive.action
  send-url: http://*************/SmsHttpInterface/smsService/Do-sendSms.action

# 腾讯地图
qqmap:
  js-key: QVUBZ-DRSCC-IRF26-AOTS5-2GRN7-DNFHU

# vaptcha
vaptcha:
  id:
  secret-key:
  send-url: http://sms.vaptcha.com/send
  verify-url: http://0.vaptcha.com/verify

# WebSocket
ws:
  base-url: ws://localhost

# 事件订阅
event:
  topic:
    pattern: "health-code:topic:*"
    alarm-ring: "health-code:topic:alarm:ring"

# 微信公众平台
wx:
  # 企业微信
  cp:
    enabled: false
    corp-id:
    corp-secret:
    redirect:
  # 小程序
  ma:
    # 汕头防疫
    stfy:
      app-id: wxaa61d489d20e90ea
      secret: 90f67dff6d17355594046024e7dc1c49
    # 金凤码
    jfm:
      app-id: wx901527d87e21f5da
      secret: 78b89b7663690f8a0edcbfc4c835b98b
      qrcode-env-version: trial
    msg-data-format: JSON
  # 粤政易
  yzy:
    enabled: false
    base-api-url: https://zwwx.gdzwfw.gov.cn
    corp-id:
    corp-secret:
    redirect:
