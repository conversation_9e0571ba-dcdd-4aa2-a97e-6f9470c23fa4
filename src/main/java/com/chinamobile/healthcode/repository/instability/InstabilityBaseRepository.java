package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.InstabilityBaseEntity;
import com.chinamobile.healthcode.repository.subject.DefaultAbstractEntityRepository;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.util.ChineseUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManagerFactory;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;

/**
 * 村（社）专项管理基础实体类
 */
@ErrorCode(module = "061")
public abstract class InstabilityBaseRepository<T extends InstabilityBaseEntity> extends DefaultAbstractEntityRepository<T> {
    public static final String ADMIN_ROLE_NAME = "村（社）专项管理员";
    public static final Logger LOGGER = LoggerFactory.getLogger(InstabilityBaseRepository.class);

    protected final DefaultDepartmentRepository departmentRepository;
    protected final DefaultResultParser resultParser;

    protected InstabilityBaseRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Class<T> tClass,
            Validator validator,
            DefaultDepartmentRepository departmentRepository,
            DefaultResultParser resultParser
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass, validator);
        this.departmentRepository = departmentRepository;
        this.resultParser = resultParser;
    }

    @Transactional(readOnly = true)
    public Result<T> getById(String id) {
        Result<T> item = new Result<>();

        if (!StringUtils.hasText(id)) {
            item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass.getSimpleName()});
            return item;
        }

        item.data = getCurrentSession().get(tClass, id);
        if (item.data == null) {
            item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass});
        }

        return item;
    }

    /**
     * 根据非id标识列获取数据
     *
     * @param t 查询数据条件
     * @return 数据
     */
    @Transactional(readOnly = true)
    public Result<T> getByUniqueColumnsAsResult(T t) {
        T existingT = getByUniqueColumns(t);
        Result<T> result;
        if (existingT == null) {
            result = new Result<>(Result.DATABASE_RECORD_NOT_FOUND);
        } else {
            result = new Result<>();
            result.data = existingT;
        }
        return result;
    }

    @Transactional(readOnly = true)
    public List<T> listByRegionId(String regionId) {
        return stream(tClass).where(i -> i.getRegionId().equals(regionId)).toList();
    }

    /**
     * 根据非id标识列获取数据
     *
     * @param t 查询数据条件
     * @return 数据
     */
    @Transactional(readOnly = true)
    public T getByUniqueColumns(T t) {
        String regionId = t.getRegionId();
        return stream(tClass)
                .where(i -> i.getRegionId().equals(regionId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据非id标识列判断是否重复数据
     *
     * @param t 查询数据条件
     * @return 是否重复数据
     */
    @Transactional(readOnly = true)
    public boolean isDuplicate(T t) {
        return getByUniqueColumnsAsResult(t).isOK();
    }

    /**
     * 获取相关标识列
     *
     * @return 标识列
     */
    public String[] getIdentityColumns() {
        return new String[]{"id"};
    }

    /**
     * @return 是否检查数据单位归属
     */
    public boolean checkAuthority() {
        return true;
    }

    /**
     * 数据校验
     *
     * @param t 数据对象
     * @return 数据校验结果
     */
    public Result<Void> validateBeforeSaveOrUpdate(T t) {
        Result<Void> validResult = new Result<>();

        Result<DefaultDepartment> deptResult = departmentRepository.get(t.getRegionId(), true);
        if (!deptResult.isOK() || deptResult.data.getLevel() != 3) {
            validResult.setCode(Result.ENUM_ERROR.P, 9, new Object[]{"请选择村、社区一级行政区域"});
        }

        return validResult;
    }

    public Result<String> saveOrUpdate(T t, User user) {
        Result<String> opResult = new Result<>();

        // 读取下级单位
        List<DefaultDepartment> subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);

        Result<Void> validResult;
        // 非法数据
        if (t == null) {
            opResult.setCode(Result.DATA_VALIDATE_ERROR);
            return opResult;
        } else if (checkAuthority()
                && subordinates.stream().noneMatch(j -> Objects.equals(j.getId(), t.getRegionId()))) {
            opResult.setCode(Result.ENUM_ERROR.P, 4);
            return opResult;
        } else if (!(validResult = validateBeforeSaveOrUpdate(t)).isOK()) {
            opResult.pack(validResult);
            return opResult;
        }

        // 更新目标
        T updateTarget = Optional.of(t)
                .map(T::getId)
                .map(this::getById)
                .filter(Result::isOK)
                .map(r -> r.data)
                .orElse(null);

        // 重复数据
        if (updateTarget == null && isDuplicate(t)) {
            opResult.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return opResult;
        }

        Result<?> execResult;
        if (updateTarget == null) {
            execResult = add(t, user.getId());
        } else {
            copyProperties(t, updateTarget, getIdentityColumns());
            execResult = update(updateTarget, user.getId());
        }

        if (execResult.isOK()) {
            opResult.data = updateTarget == null ? t.getId() : updateTarget.getId();
        } else {
            opResult.pack(execResult);
        }
        return opResult;
    }

    public Result<T> remove(String id, User user) {
        Result<T> removeResult = new Result<>();

        Result<T> item = getById(id);
        if (!item.isOK()) {
            return removeResult.pack(item);
        }

        // 非下级单位
        if (checkAuthority()
                && departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .noneMatch(i -> i.getId().equals(item.data.getRegionId()))) {
            item.setCode(Result.DATA_ACCESS_DENY);
            return item;
        }

        getCurrentSession().remove(item.data);
        getCurrentSession().flush();

        return removeResult;
    }

    public abstract Result<T> excelRowHandler(List<DefaultDepartment> subordinates, List<DefaultDepartment> departments, Map<String, Integer> header, String[] rowData, User user) throws IllegalArgumentException;

    public Result<T> defaultExcelRowHandler(List<DefaultDepartment> subordinates,
                                            List<DefaultDepartment> departments,
                                            Map<String, Integer> header,
                                            String[] rowData,
                                            User user) {
        Result<T> rowHandleResult = new Result<>();
        if (checkAuthority()) {
            String regionFullName = rowData[header.get("行政区域")];
            Department region = departments.stream()
                    .filter(j -> Objects.equals(regionFullName, j.getFullName()))
                    .findFirst().orElse(null);
            if (region == null) {
                rowHandleResult.setCode(Result.ENUM_ERROR.P, 3);
                return rowHandleResult;
            }

            // 非下级单位
            if (checkAuthority()
                    && subordinates.stream().noneMatch(j -> Objects.equals(j.getId(), region.getId()))) {
                rowHandleResult.setCode(Result.ENUM_ERROR.P, 4);
                return rowHandleResult;
            }
        }
        return excelRowHandler(subordinates, departments, header, rowData, user);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(MultipartFile multipartFile, int sheetNum, int headerRowNum, User user) {
        Result<String> importResult = new Result<>();
        File file = null;

        try {
            file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(multipartFile.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);

            // 读取下级单位
            List<DefaultDepartment> subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);

            // 读取部门
            List<DefaultDepartment> departments = departmentRepository.query(null, null, null, true, null);

            importResult = readExcel((header, lines, results) -> {
                for (int i = 0; i < lines.size(); i++) {
                    if (results.get(i).isOK()) {
                        String[] row = lines.get(i);

                        try {
                            Result<T> rowHandleResult = defaultExcelRowHandler(subordinates, departments, header, row, user);
                            T rowEntity;
                            if (rowHandleResult.isOK()) {
                                rowEntity = rowHandleResult.data;
                            } else {
                                results.set(i, rowHandleResult);
                                continue;
                            }
                            T existingT = getByUniqueColumns(rowEntity);
                            if (existingT == null) {
                                add(rowEntity, user.getId());
                            } else {
                                copyProperties(rowEntity, existingT, getIdentityColumns());
                                update(existingT, user.getId());
                            }
                        } catch (ConstraintViolationException cve) {
                            Result<?> errorResult = new Result<>();
                            errorResult.setCode(Result.ENUM_ERROR.P, 8, new Object[]{cve.getMessage()});
                            results.set(i, errorResult);
                        } catch (Exception e) {
                            Result<?> errorResult = resultParser.fromException(e, false);
                            if (errorResult == null) {
                                errorResult = new Result<>();
                                errorResult.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
                            }
                            results.set(i, errorResult);
                        }
                    }
                }
            }, file, sheetNum, headerRowNum);
        } catch (Exception e) {
            LOGGER.error("导入{}文件失败", tClass.getSimpleName(), e);
            importResult.setCode(Result.ENUM_ERROR.P, 5);
        } finally {
            if (file != null) {
                try {
                    Files.delete(file.toPath());
                } catch (IOException e) {
                    LOGGER.warn("文件{}关闭失败", file.getAbsolutePath(), e);
                }
            }
        }
        return importResult;
    }

    protected void assertContainsChinese(String columnName, String columnValue) {
        // 不考虑必填、空字符串
        if (!StringUtils.hasLength(columnValue)) {
            return;
        }

        if (!ChineseUtil.containsChinese(columnValue)) {
            throw new IllegalArgumentException(String.format("%s列填写内容须包含中文", columnName));
        }
    }

    protected void assertOnlyChinese(String columnName, String columnValue) {
        // 不考虑必填、空字符串
        if (!StringUtils.hasLength(columnValue)) {
            return;
        }

        if (!ChineseUtil.isOnlyChinese(columnValue)) {
            throw new IllegalArgumentException(String.format("%s列只支持填写中文", columnName));
        }
    }
}
