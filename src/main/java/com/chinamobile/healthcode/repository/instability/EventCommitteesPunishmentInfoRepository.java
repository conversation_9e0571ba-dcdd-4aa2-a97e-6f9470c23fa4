package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.EventCommitteesPunishmentInfo;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DefaultDateUtil;
import com.google.gson.JsonObject;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.chinamobile.healthcode.repository.subject.EventRepository;

@Repository
public class EventCommitteesPunishmentInfoRepository extends InstabilityEventInfoBaseRepository<EventCommitteesPunishmentInfo> {
    private final DefaultRoleRepository roleRepository;
    private final DictionaryRepository dictionaryRepository;

    public EventCommitteesPunishmentInfoRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Validator validator,
            DefaultDepartmentRepository departmentRepository,
            DefaultRoleRepository roleRepository,
            DictionaryRepository dictionaryRepository,
            DefaultResultParser resultParser,
            EventRepository eventRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, EventCommitteesPunishmentInfo.class, validator, departmentRepository, resultParser, eventRepository);
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
    }

    @Transactional(readOnly = true)
    public PagingItems<EventCommitteesPunishmentInfo> fuzzy(
            int count,
            int index,
            String regionFullName,
            User user
    ) {
        PagingItems<EventCommitteesPunishmentInfo> paging = new PagingItems<>(count, index);
        JinqStream<EventCommitteesPunishmentInfo> stream = stream(EventCommitteesPunishmentInfo.class);

        if (!roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE_NAME)) {
            String userDepartmentName = user.getDeptFullName();
            String pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> userDepartmentName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        paging.total = stream.count();

        if (count >= 0 && index >= 0) {
            stream = stream.skip((long) count * index).limit(count);
        }
        paging.items = stream.toList();

        return paging;
    }

    @Transactional(readOnly = true)
    public Result<JsonObject> getCategories() {
        Result<JsonObject> categoriesResult = new Result<>();
        String categories = dictionaryRepository.getVal("重点事件情况分类", "2021年以来“两委”干部涉治安、刑事处罚情况", true);
        categoriesResult.data = Optional.ofNullable(categories)
                .map(cat -> ConverterUtil.json2Object(cat, JsonObject.class))
                .orElse(null);
        return categoriesResult;
    }

    @Transactional(readOnly = true)
    public Result<EventCommitteesPunishmentInfo> get(String id) {
        Result<EventCommitteesPunishmentInfo> getResult = new Result<>();

        if (!StringUtils.hasLength(id)) {
            getResult.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{EventCommitteesPunishmentInfo.class.getSimpleName()});
            return getResult;
        }

        getResult.data = getCurrentSession().get(EventCommitteesPunishmentInfo.class, id);
        if (getResult.data == null) {
            getResult.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{EventCommitteesPunishmentInfo.class.getSimpleName()});
        }

        return getResult;
    }

    @Override
    public Result<EventCommitteesPunishmentInfo> excelRowHandler(List<DefaultDepartment> subordinates, List<DefaultDepartment> departments, Map<String, Integer> header, String[] rowData, User user) {
        Result<EventCommitteesPunishmentInfo> importResult = new Result<>();
        EventCommitteesPunishmentInfo eventInfo = new EventCommitteesPunishmentInfo();

        String columnName = "";
        try {
            columnName = "行政区域";
            String regionFullName = rowData[header.get(columnName)];
            Department region = departments.stream()
                    .filter(j -> Objects.equals(regionFullName, j.getFullName()))
                    .findFirst().orElse(null);
            if (region == null) {
                importResult.setCode(Result.ENUM_ERROR.P, 3);
                return importResult;
            } else if (region.getLevel() < 3 || region.getLevel() > 4) {
                importResult.setCode(Result.ENUM_ERROR.P, 9, new Object[]{"请选择村、社区或网格一级行政区域"});
                return importResult;
            }
            eventInfo.setRegionId(region.getId());
            eventInfo.setRegionFullName(region.getFullName());

            columnName = "姓名";
            String text = rowData[header.get(columnName)];
            assertOnlyChinese(columnName, text);
            eventInfo.setName(text);

            columnName = "职务";
            text = rowData[header.get(columnName)];
            assertOnlyChinese(columnName, text);
            eventInfo.setPosition(text);

            columnName = "事件标题";
            text = rowData[header.get(columnName)];
            assertContainsChinese(columnName, text);
            eventInfo.setTitle(text);

            columnName = "事件分类";
            text = rowData[header.get(columnName)];
            eventInfo.setCategory(text);

            columnName = "事件类型";
            text = rowData[header.get(columnName)];
            eventInfo.setType(text);

            columnName = "事件概述";
            text = rowData[header.get(columnName)];
            assertContainsChinese(columnName, text);
            eventInfo.setOverview(text);

            columnName = "事发时间";
            text = rowData[header.get(columnName)];
            eventInfo.setIncidentTime(DefaultDateUtil.from(text, true));

            validate(eventInfo);
        } catch (NullPointerException npe) {
            throw new IllegalArgumentException(String.format("未找到%s列", columnName));
        } catch (ParseException e) {
            throw new IllegalArgumentException(String.format("%s列时间格式错误，请使用yyyy/MM/dd HH:mm", columnName));
        }

        importResult.data = eventInfo;

        return importResult;
    }

    @Override
    public EventCommitteesPunishmentInfo getByUniqueColumns(EventCommitteesPunishmentInfo eventInfo) {
        String regionId = eventInfo.getRegionId();
        String title = eventInfo.getTitle();
        String overview = eventInfo.getOverview();
        return stream(tClass)
                .where(i -> i.getRegionId().equals(regionId))
                .where(i -> i.getTitle().equals(title))
                .where(i -> i.getOverview().equals(overview))
                .findFirst()
                .orElse(null);
    }
}
