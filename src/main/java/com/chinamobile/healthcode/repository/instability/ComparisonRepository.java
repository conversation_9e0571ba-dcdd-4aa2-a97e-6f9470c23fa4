package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.*;
import com.chinamobile.healthcode.model.subject.EventDescription;
import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.healthcode.model.subject.PlaceDescription;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple8;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import java.util.stream.Stream;

@Repository
@Transactional(readOnly = true)
@ErrorCode(module = "061")
public class ComparisonRepository extends AbstractJinqRepository {
    private final DefaultDepartmentRepository departmentRepository;
    private final DefaultRoleRepository roleRepository;

    private final BasicInfoRepository basicInfoRepository;
    private final PersonnelInfoRepository personnelInfoRepository;
    private final PlaceInfoRepository placeInfoRepository;

    public ComparisonRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                                DefaultDepartmentRepository departmentRepository,
                                DefaultRoleRepository roleRepository,
                                BasicInfoRepository basicInfoRepository,
                                PersonnelInfoRepository personnelInfoRepository,
                                PlaceInfoRepository placeInfoRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.basicInfoRepository = basicInfoRepository;
        this.personnelInfoRepository = personnelInfoRepository;
        this.placeInfoRepository = placeInfoRepository;
    }

    public Result<PagingItems<Tuple8<String, String, Long, Long, Long, Long, Long, Long>>> fuzzy(int count, int index, String regionId, User user) {
        Result<PagingItems<Tuple8<String, String, Long, Long, Long, Long, Long, Long>>> fuzzyResult = new Result<>();

        // 村社区列表，分页总数有用
        List<DefaultDepartment> regionList;
        if (user == null) {
            regionList = departmentRepository.subordinates(regionId, 3, null)
                    .stream()
                    .filter(DefaultDepartment::getIsEnabled)
                    .collect(Collectors.toList());
        } else {
            regionList = departmentRepository.subordinatesByUser(regionId, InstabilityBaseRepository.ADMIN_ROLE_NAME, 3, null, false, user);
        }
        Stream<DefaultDepartment> regionStream = regionList
                .stream()
                .sorted(Comparator.comparing(DefaultDepartment::getCode).thenComparing(DefaultDepartment::getSeq));
        if (count > -1 && index > -1) {
            regionStream = regionStream.skip((long) count * index).limit(count);
        }
        // 排序分页后村社区id列表
        List<String> regionIdList = regionStream.map(DefaultDepartment::getId).collect(Collectors.toList());

        // 各类型事件数统计
        List<Pair<String, Long>> eventCrimeInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCrimeInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventPetitionInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventPetitionInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventOnlineOpinionInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventOnlineOpinionInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventCrowdGatheringInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCrowdGatheringInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventCommitteesPunishmentInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCommitteesPunishmentInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventCommitteesDisciplinaryInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCommitteesDisciplinaryInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventRegionalCrimeInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventRegionalCrimeInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventIssuesChallengesInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventIssuesChallengesInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventOtherInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventOtherInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        Map<String, Long> eventCount = Stream.of(eventCrimeInfoCount,
                        eventPetitionInfoCount,
                        eventOnlineOpinionInfoCount,
                        eventCrowdGatheringInfoCount,
                        eventCommitteesPunishmentInfoCount,
                        eventCommitteesDisciplinaryInfoCount,
                        eventRegionalCrimeInfoCount,
                        eventIssuesChallengesInfoCount,
                        eventOtherInfoCount)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(Pair::getOne, Collectors.summingLong(Pair::getTwo)));

        // 统计重点人员、重点场所、重点事件数量
        Map<String, Long> subjectPersonCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, personSource) -> personSource.stream(PersonDescription.class), (region, person) -> person.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toMap(Pair::getOne, Pair::getTwo));
        Map<String, Long> subjectPlaceCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, placeSource) -> placeSource.stream(PlaceDescription.class), (region, place) -> place.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toMap(Pair::getOne, Pair::getTwo));
        Map<String, Long> subjectEventCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventDescription.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toMap(Pair::getOne, Pair::getTwo));

        List<Tuple8<String, String, Long, Long, Long, Long, Long, Long>> comparisonList = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, source) -> source.stream(PersonnelInfo.class), (region, personnel) -> region.getId().equals(personnel.getRegionId()))
                .leftOuterJoin((pair, source) -> source.stream(PlaceInfo.class), (pair, place) -> pair.getOne().getId().equals(place.getRegionId()))
                .sortedBy(p -> p.getOne().getOne().getSeq())
                .sortedBy(p -> p.getOne().getOne().getCode())
                .map(p -> {
                    DefaultDepartment region = p.getOne().getOne();
                    PersonnelInfo personnelInfo = p.getOne().getTwo();
                    Long numOfPersonnel = Optional.ofNullable(personnelInfo)
                            .filter(Objects::nonNull)
                            .map(i -> LongStream.of(
                                            i.getNumOfPoliticalSecurityKeyPersonnel(),
                                            i.getNumOfLawyerKeyPersonnel(),
                                            i.getNumOfCultMembers(),
                                            i.getNumOfPetitionKeyPersonnel(),
                                            i.getNumOfOnlineKeyPersonnel(),
                                            i.getNumOfFinanciallyAffectedPersons(),
                                            i.getNumOfMilitaryVeterans(),
                                            i.getNumOfVaccineVictims(),
                                            i.getNumOfThreeNewPersonnel(),
                                            i.getNumOfSevereMentalDisorderPatients(),
                                            i.getNumOfReleasedPrisoners(),
                                            i.getNumOfCommunityCorrectionPersonnel(),
                                            i.getNumOfDrugAddicts(),
                                            i.getNumOfEightCategoriesKeyMinors(),
                                            i.getNumOfThreeLossesOneBiasPersonnel(),
                                            i.getNumOfRegisteredLowIncomePersonnel()
                                    )
                                    .sum())
                            .orElse(0L);
                    PlaceInfo placeInfo = p.getTwo();
                    Long numOfPlaces = Optional.ofNullable(placeInfo)
                            .filter(Objects::nonNull)
                            .map(i -> LongStream.of(
                                                    i.getNumOfGovernmentAgencies(),
                                                    i.getNumOfBusinessAreas(),
                                                    i.getNumOfStations(),
                                                    i.getNumOfDocks(),
                                                    i.getNumOfSquares(),
                                                    i.getNumOfLandmarkBuildings(),
                                                    i.getNumOfOverpasses(),
                                                    i.getNumOfLedDisplays(),
                                                    i.getNumOfCulturalAndSportsVenues(),
                                                    i.getNumOfTouristAttractions(),
                                                    i.getNumOfSchools(),
                                                    i.getNumOfMedicalInstitutions(),
                                                    i.getNumOfPetitionReceptionPlaces(),
                                                    i.getNumOfReligiousPlaces(),
                                                    i.getNumOfEntertainmentPlaces()
                                            )
                                            .sum()
                            )
                            .orElse(0L);
                    Long numOfEvents = eventCount.getOrDefault(region.getId(), 0L);

                    Long numOfSubjectPerson = subjectPersonCount.getOrDefault(region.getId(), 0L);
                    Long numOfSubjectPlace = subjectPlaceCount.getOrDefault(region.getId(), 0L);
                    Long numOfSubjectEvent = subjectEventCount.getOrDefault(region.getId(), 0L);
                    return new Tuple8<>(region.getId(), region.getFullName(), numOfPersonnel, numOfSubjectPerson, numOfPlaces, numOfSubjectPlace, numOfEvents, numOfSubjectEvent);
                })
                .collect(Collectors.toList());

        PagingItems<Tuple8<String, String, Long, Long, Long, Long, Long, Long>> pagingItems = new PagingItems<>();
        pagingItems.total = regionList.size();
        pagingItems.items = comparisonList;
        fuzzyResult.data = pagingItems;

        return fuzzyResult;
    }

    @Transactional(readOnly = true)
    public Result<String> export(String regionId, User user) throws IOException {
        Result<String> exportResult = new Result<>();
        DefaultDepartment region;

        if (!StringUtils.hasLength(regionId)) {
            exportResult.setCode(Result.ENUM_ERROR.P, 6, new Object[]{"行政区域"});
            return exportResult;
        } else if (!roleRepository.isUserInRole(user.getId(), InstabilityBaseRepository.ADMIN_ROLE_NAME)
                && departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .noneMatch(i -> Objects.equals(regionId, i.getId()))) {
            exportResult.setCode(Result.DATA_ACCESS_DENY);
            return exportResult;
        } else {
            Result<DefaultDepartment> regionResult = departmentRepository.get(regionId, true);
            if (!regionResult.isOK()) {
                exportResult.setCode(Result.ENUM_ERROR.P, 6, new Object[]{"行政区域"});
                return exportResult;
            } else {
                region = regionResult.data;
            }
        }

        // 从视图查询数据
        List<Object[]> viewData = getCurrentSession().createNativeQuery(
                "SELECT * FROM instability_subject_comparison WHERE `村/社区` = :regionFullName")
                .setParameter("regionFullName", region.getFullName())
                .getResultList();

        exportResult.data = POIUtil.exportToWorkbookBase64String((workbook) -> {
            Sheet sheet = workbook.createSheet("数据量对比表");
            
            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            
            // 创建数据行样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"行政区域", "村社区管理——政治安全重点人员", "专题管理——政治安全重点人员", "村社区管理——律师重点人员",
                    "专题管理——律师重点人员", "村社区管理——邪教人员", "专题管理——邪教人员", "村社区管理——信访重点人员",
                    "专题管理——信访重点人员", "村社区管理——网络重点人员", "专题管理——网络重点人员", "村社区管理——涉众金融投资受损人员",
                    "专题管理——涉众金融投资受损人员", "村社区管理——军队退役人员", "专题管理——军队退役人员", "村社区管理——疫苗“受害”人员",
                    "专题管理——疫苗“受害”人员", "村社区管理——“三新”从业人员", "专题管理——“三新”从业人员", "村社区管理——严重精神障碍患者",
                    "专题管理——严重精神障碍患者", "村社区管理——刑满释放安置帮教人员", "专题管理——刑满释放安置帮教人员", "村社区管理——社区矫正人员",
                    "专题管理——社区矫正人员", "村社区管理——吸毒人员", "专题管理——吸毒人员", "村社区管理——“八类”重点未成年人",
                    "专题管理——“八类”重点未成年人", "村社区管理——“三失一偏”(生活失意、心态失衡、行为失常人员和性格偏执)人员",
                    "专题管理——“三失一偏”(生活失意、心态失衡、行为失常人员和性格偏执)人员", "村社区管理——低保在册人员",
                    "专题管理——低保在册人员", "村社区管理——党政机关", "专题管理——党政机关", "村社区管理——繁华商圈",
                    "专题管理——繁华商圈", "村社区管理——车站", "专题管理——车站", "村社区管理——码头", "专题管理——码头",
                    "村社区管理——广场", "专题管理——广场", "村社区管理——标志性建筑物", "专题管理——标志性建筑物", "村社区管理——天桥",
                    "专题管理——天桥", "村社区管理——公共电子显示屏", "专题管理——公共电子显示屏", "村社区管理——文体场馆", "专题管理——文体场馆",
                    "村社区管理——旅游景区", "专题管理——旅游景区", "村社区管理——学校", "专题管理——学校", "村社区管理——医疗机构",
                    "专题管理——医疗机构", "村社区管理——信访接待场所", "专题管理——信访接待场所", "村社区管理——宗教场所", "专题管理——宗教场所",
                    "村社区管理——休闲娱乐场所", "专题管理——休闲娱乐场所", "村社区管理——2021年以来发生刑事案件情况",
                    "专题管理——2021年以来发生刑事案件情况", "村社区管理——2021年以来到市、省、京信访情况",
                    "专题管理——2021年以来到市、省、京信访情况", "村社区管理——2021年以来网络舆情及网络重点人情况",
                    "专题管理——2021年以来网络舆情及网络重点人情况", "村社区管理——2021年以来群众在村委聚集情况",
                    "专题管理——2021年以来群众在村委聚集情况", "村社区管理——2021年以来“两委”干部涉治安、刑事处罚情况",
                    "专题管理——2021年以来“两委”干部涉治安、刑事处罚情况", "村社区管理——2021年换届以来“两委”干部受党纪政纪处分情况",
                    "专题管理——2021年换届以来“两委”干部受党纪政纪处分情况", "村社区管理——地域性犯罪案件情况",
                    "专题管理——地域性犯罪案件情况", "村社区管理——群众集中反映问题及基层治理难点、痛点",
                    "专题管理——群众集中反映问题及基层治理难点、痛点", "村社区管理——其他情况", "专题管理——其他情况"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 20 * 256);
            }

            // 填充数据
            int rowNum = 1;
            for (Object[] rowData : viewData) {
                Row dataRow = sheet.createRow(rowNum++);
                for (int i = 0; i < rowData.length; i++) {
                    Cell cell = dataRow.createCell(i);
                    if (rowData[i] != null) {
                        cell.setCellValue(rowData[i].toString());
                    }
                    cell.setCellStyle(dataStyle);
                }
            }

            // 冻结首行
            sheet.createFreezePane(0, 1);
        }, "xlsx");

        return exportResult;
    }
}
