package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.*;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple5;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import java.util.stream.Stream;

@Repository
@Transactional(readOnly = true)
@ErrorCode(module = "061")
public class StatisticsRepository extends AbstractJinqRepository {
    private final DefaultDepartmentRepository departmentRepository;
    private final DefaultRoleRepository roleRepository;

    private final BasicInfoRepository basicInfoRepository;
    private final PersonnelInfoRepository personnelInfoRepository;
    private final PlaceInfoRepository placeInfoRepository;

    public StatisticsRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                                DefaultDepartmentRepository departmentRepository,
                                DefaultRoleRepository roleRepository,
                                BasicInfoRepository basicInfoRepository,
                                PersonnelInfoRepository personnelInfoRepository,
                                PlaceInfoRepository placeInfoRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.basicInfoRepository = basicInfoRepository;
        this.personnelInfoRepository = personnelInfoRepository;
        this.placeInfoRepository = placeInfoRepository;
    }

    public Result<PagingItems<Tuple5<String, String, Long, Long, Long>>> fuzzy(int count, int index, String regionId, User user) {
        Result<PagingItems<Tuple5<String, String, Long, Long, Long>>> fuzzyResult = new Result<>();

        // 村社区列表，分页总数有用
        List<DefaultDepartment> regionList = departmentRepository.subordinatesByUser(regionId, InstabilityBaseRepository.ADMIN_ROLE_NAME, 3, null, false, user);
        Stream<DefaultDepartment> regionStream = regionList
                .stream()
                .sorted(Comparator.comparing(DefaultDepartment::getCode).thenComparing(DefaultDepartment::getSeq));
        if (count > -1 && index > -1) {
            regionStream = regionStream.skip((long) count * index).limit(count);
        }
        // 排序分页后村社区id列表
        List<String> regionIdList = regionStream.map(DefaultDepartment::getId).collect(Collectors.toList());

        // 各类型事件数统计
        List<Pair<String, Long>> eventCrimeInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCrimeInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventPetitionInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventPetitionInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventOnlineOpinionInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventOnlineOpinionInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventCrowdGatheringInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCrowdGatheringInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventCommitteesPunishmentInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCommitteesPunishmentInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventCommitteesDisciplinaryInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventCommitteesDisciplinaryInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventRegionalCrimeInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventRegionalCrimeInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventIssuesChallengesInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventIssuesChallengesInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        List<Pair<String, Long>> eventOtherInfoCount = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, eventSource) -> eventSource.stream(EventOtherInfo.class), (region, event) -> event.getRegionFullName().contains(region.getFullName()))
                .where(p -> p.getTwo() != null)
                .group(p -> p.getOne().getId(), (p, stream) -> stream.count())
                .collect(Collectors.toList());
        Map<String, Long> eventCount = Stream.of(eventCrimeInfoCount,
                        eventPetitionInfoCount,
                        eventOnlineOpinionInfoCount,
                        eventCrowdGatheringInfoCount,
                        eventCommitteesPunishmentInfoCount,
                        eventCommitteesDisciplinaryInfoCount,
                        eventRegionalCrimeInfoCount,
                        eventIssuesChallengesInfoCount,
                        eventOtherInfoCount)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(Pair::getOne, Collectors.summingLong(Pair::getTwo)));
        List<Tuple5<String, String, Long, Long, Long>> statsList = stream(DefaultDepartment.class)
                .where(d -> JPQL.isInList(d.getId(), regionIdList))
                .leftOuterJoin((region, source) -> source.stream(PersonnelInfo.class), (region, personnel) -> region.getId().equals(personnel.getRegionId()))
                .leftOuterJoin((pair, source) -> source.stream(PlaceInfo.class), (pair, place) -> pair.getOne().getId().equals(place.getRegionId()))
                .sortedBy(p -> p.getOne().getOne().getSeq())
                .sortedBy(p -> p.getOne().getOne().getCode())
                .map(p -> {
                    DefaultDepartment region = p.getOne().getOne();
                    PersonnelInfo personnelInfo = p.getOne().getTwo();
                    Long numOfPersonnel = Optional.ofNullable(personnelInfo)
                            .filter(Objects::nonNull)
                            .map(i -> LongStream.of(
                                            i.getNumOfPoliticalSecurityKeyPersonnel(),
                                            i.getNumOfLawyerKeyPersonnel(),
                                            i.getNumOfCultMembers(),
                                            i.getNumOfPetitionKeyPersonnel(),
                                            i.getNumOfOnlineKeyPersonnel(),
                                            i.getNumOfFinanciallyAffectedPersons(),
                                            i.getNumOfMilitaryVeterans(),
                                            i.getNumOfVaccineVictims(),
                                            i.getNumOfThreeNewPersonnel(),
                                            i.getNumOfSevereMentalDisorderPatients(),
                                            i.getNumOfReleasedPrisoners(),
                                            i.getNumOfCommunityCorrectionPersonnel(),
                                            i.getNumOfDrugAddicts(),
                                            i.getNumOfEightCategoriesKeyMinors(),
                                            i.getNumOfThreeLossesOneBiasPersonnel(),
                                            i.getNumOfRegisteredLowIncomePersonnel()
                                    )
                                    .sum())
                            .orElse(0L);
                    PlaceInfo placeInfo = p.getTwo();
                    Long numOfPlaces = Optional.ofNullable(placeInfo)
                            .filter(Objects::nonNull)
                            .map(i -> LongStream.of(
                                                    i.getNumOfGovernmentAgencies(),
                                                    i.getNumOfBusinessAreas(),
                                                    i.getNumOfStations(),
                                                    i.getNumOfDocks(),
                                                    i.getNumOfSquares(),
                                                    i.getNumOfLandmarkBuildings(),
                                                    i.getNumOfOverpasses(),
                                                    i.getNumOfLedDisplays(),
                                                    i.getNumOfCulturalAndSportsVenues(),
                                                    i.getNumOfTouristAttractions(),
                                                    i.getNumOfSchools(),
                                                    i.getNumOfMedicalInstitutions(),
                                                    i.getNumOfPetitionReceptionPlaces(),
                                                    i.getNumOfReligiousPlaces(),
                                                    i.getNumOfEntertainmentPlaces()
                                            )
                                            .sum()
                            )
                            .orElse(0L);
                    Long numOfEvents = eventCount.getOrDefault(region.getId(), 0L);
                    return new Tuple5<>(region.getId(), region.getFullName(), numOfPersonnel, numOfPlaces, numOfEvents);
                })
                .collect(Collectors.toList());

        PagingItems<Tuple5<String, String, Long, Long, Long>> pagingItems = new PagingItems<>();
        pagingItems.total = regionList.size();
        pagingItems.items = statsList;
        fuzzyResult.data = pagingItems;

        return fuzzyResult;
    }

    @Transactional(readOnly = true)
    public Result<String> export(String regionId, User user) throws IOException {
        Result<String> exportResult = new Result<>();
        DefaultDepartment region;

        if (!StringUtils.hasLength(regionId)) {
            exportResult.setCode(Result.ENUM_ERROR.P, 6, new Object[]{"行政区域"});
            return exportResult;
        } else if (!roleRepository.isUserInRole(user.getId(), InstabilityBaseRepository.ADMIN_ROLE_NAME)
                && departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .noneMatch(i -> Objects.equals(regionId, i.getId()))) {
            exportResult.setCode(Result.DATA_ACCESS_DENY);
            return exportResult;
        } else {
            Result<DefaultDepartment> regionResult = departmentRepository.get(regionId, true);
            if (!regionResult.isOK()) {
                exportResult.setCode(Result.ENUM_ERROR.P, 6, new Object[]{"行政区域"});
                return exportResult;
            } else {
                region = regionResult.data;
            }
        }

        String regionFullName = region.getFullName();

        BasicInfo basicInfo = stream(BasicInfo.class).where(b -> b.getRegionId().equals(regionId)).findFirst().orElse(null);
        PlaceInfo placeInfo = stream(PlaceInfo.class).where(p -> p.getRegionId().equals(regionId)).findFirst().orElse(null);
        PersonnelInfo personnelInfo = stream(PersonnelInfo.class).where(p -> p.getRegionId().equals(regionId)).findFirst().orElse(null);
        OrganizationInfo organizationInfo = stream(OrganizationInfo.class).where(p -> p.getRegionId().equals(regionId)).findFirst().orElse(null);
        List<EventCrimeInfo> eventCrimeInfoList = stream(EventCrimeInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventPetitionInfo> eventPetitionInfoList = stream(EventPetitionInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventOnlineOpinionInfo> eventOnlineOpinionInfoList = stream(EventOnlineOpinionInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventCrowdGatheringInfo> eventCrowdGatheringInfoList = stream(EventCrowdGatheringInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventCommitteesPunishmentInfo> eventCommitteesPunishmentInfoList = stream(EventCommitteesPunishmentInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventCommitteesDisciplinaryInfo> eventCommitteesDisciplinaryInfoList = stream(EventCommitteesDisciplinaryInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventRegionalCrimeInfo> eventRegionalCrimeInfoList = stream(EventRegionalCrimeInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventIssuesChallengesInfo> eventIssuesChallengesInfoList = stream(EventIssuesChallengesInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());
        List<EventOtherInfo> eventOtherInfoList = stream(EventOtherInfo.class).where(e -> e.getRegionFullName().contains(regionFullName)).collect(Collectors.toList());

        exportResult.data = POIUtil.exportToWorkbookBase64String((workbook) -> {
            Sheet sheet = workbook.createSheet("汇总表");
            Row row;
            Cell cell;
            CellStyle cellStyle;
            Font font;

            // 行索引
            int rowIndex = 0;
            int startRow, endRow;
            // 序号
            int seq = 1;

            // 标题
            row = sheet.createRow(rowIndex++);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("全市村（社区）不稳定因素排查化解表");
//            sheet.setColumnWidth(0, 120 * 256);

            // 所属
            row = sheet.createRow(rowIndex++);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3));
            row.createCell(0).setCellValue(String.format("区（县）镇（街道）村（社区）：%s", region.getFullName()));

            // 列头
            row = sheet.createRow(rowIndex++);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            font = workbook.createFont();
            font.setBold(true);
            cellStyle.setFont(font);
            cell = row.createCell(0);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("序号");
            cell = row.createCell(1);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("项目");
            cell = row.createCell(2);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("内容");
            cell = row.createCell(3);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("备注");

            // 村（社区）基本情况
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("村（社区）基本情况");

            if (basicInfo != null) {
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(String.format("(1)辖区面积%f平方公里；\n" +
                                "(2)户籍人口%d人；\n" +
                                "(3)常住人口%d人；\n" +
                                "(4)外出人口%d人，其中国内%d人、国外%d人；\n" +
                                "(5)村居“两委”%d人；\n" +
                                "(6)划分网格%d个；\n" +
                                "(7)村居主要特点以及其他相关情况：%s",
                        basicInfo.getArea(),
                        basicInfo.getRegisteredPopulation(),
                        basicInfo.getResidentPopulation(),
                        basicInfo.getOutPopulation(),
                        basicInfo.getInternationalOutPopulation(),
                        basicInfo.getDomesticOutPopulation(),
                        basicInfo.getNumOfVillageCommitteeMembers(),
                        basicInfo.getGridCount(),
                        basicInfo.getVillageInfo()));

                row.setHeight((short) (117 * 20));
                sheet.setColumnWidth(1, 60 * 256);
                sheet.setColumnWidth(2, 100 * 256);
            }

            // 重点场所情况
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("重点场所情况");

            if (placeInfo != null) {
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(String.format("(1)党政机关%d个；\n" +
                                "(2)繁华商圈%d个，其中步行街%d个、大型商场%d个、城市综合体%d个；\n" +
                                "(3)车站%d个；\n" +
                                "(4)码头%d个；\n" +
                                "(5)广场%d座；\n" +
                                "(6)标志性建筑物%d处；\n" +
                                "(7)天桥%d座；\n" +
                                "(8)公共电子显示屏%d个；\n" +
                                "(9)文体场馆%d个；\n" +
                                "(10)旅游景区%d个；\n" +
                                "(11)学校%d所，其中大学(大专)%d所 、中学%d所、小学%d所 、幼儿园%d所；\n" +
                                "(12)医疗机构%d个，其中医院%d家、卫生站%d个；\n" +
                                "(13)信访接待场所%d个；\n" +
                                "(14)宗教场所%d个，其中私人宗教聚集点%d个；\n" +
                                "(15)休闲娱乐场所%d家，其中酒店%d家、KTV%d家、酒吧%d家、歌舞厅%d家、沐足按摩%d家、棋牌室%d家、茶座%d家。",
                        placeInfo.getNumOfGovernmentAgencies(),
                        placeInfo.getNumOfBusinessAreas(),
                        placeInfo.getNumOfPedestrianStreets(),
                        placeInfo.getNumOfShoppingMalls(),
                        placeInfo.getNumOfUrbanComplexes(),
                        placeInfo.getNumOfStations(),
                        placeInfo.getNumOfDocks(),
                        placeInfo.getNumOfSquares(),
                        placeInfo.getNumOfLandmarkBuildings(),
                        placeInfo.getNumOfOverpasses(),
                        placeInfo.getNumOfLedDisplays(),
                        placeInfo.getNumOfCulturalAndSportsVenues(),
                        placeInfo.getNumOfTouristAttractions(),
                        placeInfo.getNumOfSchools(),
                        placeInfo.getNumOfCollegesAndUniversities(),
                        placeInfo.getNumOfMiddleSchools(),
                        placeInfo.getNumOfPrimarySchools(),
                        placeInfo.getNumOfKindergartens(),
                        placeInfo.getNumOfMedicalInstitutions(),
                        placeInfo.getNumOfHospitals(),
                        placeInfo.getNumOfHealthStations(),
                        placeInfo.getNumOfPetitionReceptionPlaces(),
                        placeInfo.getNumOfReligiousPlaces(),
                        placeInfo.getNumOfPrivateReligiousPlaces(),
                        placeInfo.getNumOfEntertainmentPlaces(),
                        placeInfo.getNumOfHotels(),
                        placeInfo.getNumOfKtv(),
                        placeInfo.getNumOfBars(),
                        placeInfo.getNumOfDanceHalls(),
                        placeInfo.getNumOfMassages(),
                        placeInfo.getNumOfChessAndCardRooms(),
                        placeInfo.getNumOfTeaHouses()));

                row.setHeight((short) (117 * 20));
                sheet.setColumnWidth(1, 60 * 256);
                sheet.setColumnWidth(2, 100 * 256);
            }

            // 重点人员情况
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("重点人员情况");

            if (personnelInfo != null) {
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(String.format("(1)政治安全重点人%d人；\n" +
                                "(2)律师重点人%d人；\n" +
                                "(3)邪教人员共%d人，其中法轮功%d人、全能神%d人、其他邪教人员%d人；\n" +
                                "(4)信访重点人员%d人，其中“三跨三分离”人员%d人；\n" +
                                "(5)网络重点人%d人；\n" +
                                "(6)涉众金融投资受损人员%d人；\n" +
                                "(7)军队退役人员%d人，其中涉访重点人员%d人；\n" +
                                "(8)疫苗“受害”人员%d人；\n" +
                                "(9)“三新”从业人员%d人；\n" +
                                "(10)严重精神障碍患者共%d人，其中危险性评估三级以上精神障碍患者%d人；\n" +
                                "(11)刑满释放安置帮教人员%d人，其中重点帮教人员%d人；\n" +
                                "(12)社区矫正人员%d人，其中重点社矫人员%d人；\n" +
                                "(13)吸毒人员%d人，其中社戒社康人员%d人；\n" +
                                "(14)“八类”重点未成年人%d人；\n" +
                                "(15)“三失一偏”(生活失意、心态失衡、行为失常人员和性格偏执)人员%d人；\n" +
                                "(16)低保在册人员%d人。",
                        personnelInfo.getNumOfPoliticalSecurityKeyPersonnel(),
                        personnelInfo.getNumOfLawyerKeyPersonnel(),
                        personnelInfo.getNumOfCultMembers(),
                        personnelInfo.getNumOfFaLunGongMembers(),
                        personnelInfo.getNumOfAlmightyGodMembers(),
                        personnelInfo.getNumOfOtherCultMembers(),
                        personnelInfo.getNumOfPetitionKeyPersonnel(),
                        personnelInfo.getNumOfThreeCrossThreeSeparationPersonnel(),
                        personnelInfo.getNumOfOnlineKeyPersonnel(),
                        personnelInfo.getNumOfFinanciallyAffectedPersons(),
                        personnelInfo.getNumOfMilitaryVeterans(),
                        personnelInfo.getNumOfPetitionRelatedKeyPersonnel(),
                        personnelInfo.getNumOfVaccineVictims(),
                        personnelInfo.getNumOfThreeNewPersonnel(),
                        personnelInfo.getNumOfSevereMentalDisorderPatients(),
                        personnelInfo.getNumOfHighRiskMentalDisorderPatients(),
                        personnelInfo.getNumOfReleasedPrisoners(),
                        personnelInfo.getNumOfKeyEducationAndHelpPersonnel(),
                        personnelInfo.getNumOfCommunityCorrectionPersonnel(),
                        personnelInfo.getNumOfKeyCommunityCorrectionPersonnel(),
                        personnelInfo.getNumOfDrugAddicts(),
                        personnelInfo.getNumOfCommunityDrugRehabilitations(),
                        personnelInfo.getNumOfEightCategoriesKeyMinors(),
                        personnelInfo.getNumOfThreeLossesOneBiasPersonnel(),
                        personnelInfo.getNumOfRegisteredLowIncomePersonnel()));

                row.setHeight((short) (117 * 20));
                sheet.setColumnWidth(1, 60 * 256);
                sheet.setColumnWidth(2, 100 * 256);
            }

            // 民间组织及活动情况
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("民间组织及活动情况");

            if (organizationInfo != null) {
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(String.format("(1)老人组数量%d个；\n" +
                                "(2)理事会数量%d个；\n" +
                                "(3)其他民间组织数量%d个；\n" +
                                "(4)活动情况：%s",
                        organizationInfo.getNumOfElderlyGroups(),
                        organizationInfo.getNumOfCouncils(),
                        organizationInfo.getNumOfOtherCivilOrganizations(),
                        organizationInfo.getActivitySituation()));

                row.setHeight((short) (117 * 20));
                sheet.setColumnWidth(1, 60 * 256);
                sheet.setColumnWidth(2, 100 * 256);
            }

            // 2021年以来发生刑事案件情况
            if (eventCrimeInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCrimeInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCrimeInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("2021年以来发生刑事案件情况");

            for (int i = 0; i < eventCrimeInfoList.size(); i++) {
                EventCrimeInfo event = eventCrimeInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 2021年以来到市、省、京信访情况
            if (eventPetitionInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventPetitionInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventPetitionInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("2021年以来到市、省、京信访情况");

            for (int i = 0; i < eventPetitionInfoList.size(); i++) {
                EventPetitionInfo event = eventPetitionInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 2021年以来网络舆情及网络重点人情况
            if (eventOnlineOpinionInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventOnlineOpinionInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventOnlineOpinionInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("2021年以来网络舆情及网络重点人情况");

            for (int i = 0; i < eventOnlineOpinionInfoList.size(); i++) {
                EventOnlineOpinionInfo event = eventOnlineOpinionInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 2021年以来群众在村委聚集情况
            if (eventCrowdGatheringInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCrowdGatheringInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCrowdGatheringInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("2021年以来群众在村委聚集情况");

            for (int i = 0; i < eventCrowdGatheringInfoList.size(); i++) {
                EventCrowdGatheringInfo event = eventCrowdGatheringInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 2021年以来“两委”干部涉治安、刑事处罚情况
            if (eventCommitteesPunishmentInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCommitteesPunishmentInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCommitteesPunishmentInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("2021年以来“两委”干部涉治安、刑事处罚情况");

            for (int i = 0; i < eventCommitteesPunishmentInfoList.size(); i++) {
                EventCommitteesPunishmentInfo event = eventCommitteesPunishmentInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 2021年换届以来“两委”干部受党纪政纪处分情况
            if (eventCommitteesDisciplinaryInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCommitteesDisciplinaryInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventCommitteesDisciplinaryInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("2021年换届以来“两委”干部受党纪政纪处分情况");

            for (int i = 0; i < eventCommitteesDisciplinaryInfoList.size(); i++) {
                EventCommitteesDisciplinaryInfo event = eventCommitteesDisciplinaryInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 地域性犯罪案件情况
            if (eventRegionalCrimeInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventRegionalCrimeInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventRegionalCrimeInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("地域性犯罪案件情况");

            for (int i = 0; i < eventRegionalCrimeInfoList.size(); i++) {
                EventRegionalCrimeInfo event = eventRegionalCrimeInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 群众集中反映问题及基层治理难点、痛点
            if (eventIssuesChallengesInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventIssuesChallengesInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventIssuesChallengesInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("群众集中反映问题及基层治理难点、痛点");

            for (int i = 0; i < eventIssuesChallengesInfoList.size(); i++) {
                EventIssuesChallengesInfo event = eventIssuesChallengesInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }

            // 其他情况
            if (eventOtherInfoList.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventOtherInfoList.size() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + eventOtherInfoList.size() - 1, 1, 1));
            }
            row = sheet.createRow(rowIndex++);
            cell = row.createCell(0);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(seq++);

            cell = row.createCell(1);
            cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("其他情况");

            for (int i = 0; i < eventOtherInfoList.size(); i++) {
                EventOtherInfo event = eventOtherInfoList.get(i);
                if (i > 0) {
                    row = sheet.createRow(rowIndex++);
                }
                cell = row.createCell(2);
                cellStyle = workbook.createCellStyle();
                cellStyle.setWrapText(true);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(event.getOverview());
            }
        }, "xlsx");

        return exportResult;
    }
}
