package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.OrganizationInfo;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Repository
public class OrganizationInfoRepository extends InstabilityBaseRepository<OrganizationInfo> {
    private final DefaultRoleRepository roleRepository;

    public OrganizationInfoRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Validator validator,
            DefaultDepartmentRepository departmentRepository,
            DefaultRoleRepository roleRepository,
            DefaultResultParser resultParser

    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, OrganizationInfo.class, validator, departmentRepository, resultParser);
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public PagingItems<OrganizationInfo> fuzzy(
            int count,
            int index,
            String regionFullName,
            User user
    ) {
        PagingItems<OrganizationInfo> paging = new PagingItems<>(count, index);
        JinqStream<OrganizationInfo> stream = stream(OrganizationInfo.class);

        if (!roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE_NAME)) {
            String userDepartmentName = user.getDeptFullName();
            String pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> userDepartmentName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        paging.total = stream.count();

        if (count >= 0 && index >= 0) {
            stream = stream.skip((long) count * index).limit(count);
        }
        paging.items = stream.toList();

        return paging;
    }

    @Override
    public Result<OrganizationInfo> excelRowHandler(List<DefaultDepartment> subordinates, List<DefaultDepartment> departments, Map<String, Integer> header, String[] rowData, User user) {
        Result<OrganizationInfo> importResult = new Result<>();
        OrganizationInfo organizationInfo = new OrganizationInfo();

        String regionFullName = rowData[header.get("行政区域")];
        Department region = departments.stream()
                .filter(j -> Objects.equals(regionFullName, j.getFullName()))
                .findFirst().orElse(null);
        if (region == null) {
            importResult.setCode(Result.ENUM_ERROR.P, 3);
            return importResult;
        } else if (region.getLevel() != 3) {
            importResult.setCode(Result.ENUM_ERROR.P, 9, new Object[]{"请选择村、社区一级行政区域"});
            return importResult;
        }
        organizationInfo.setRegionId(region.getId());
        organizationInfo.setRegionFullName(region.getFullName());

        String columnName = "";
        try {
            columnName = "老人组数量";
            String text = rowData[header.get(columnName)];
            organizationInfo.setNumOfElderlyGroups(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

            columnName = "理事会数量";
            text = rowData[header.get(columnName)];
            organizationInfo.setNumOfCouncils(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

            columnName = "其他民间组织数量";
            text = rowData[header.get(columnName)];
            organizationInfo.setNumOfOtherCivilOrganizations(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

            columnName = "活动情况";
            text = rowData[header.get(columnName)];
            assertContainsChinese(columnName, text);
            organizationInfo.setActivitySituation(text);
        }  catch (NullPointerException npe) {
            throw new IllegalArgumentException(String.format("未找到%s列", columnName));
        }

        importResult.data = organizationInfo;

        return importResult;
    }
}
