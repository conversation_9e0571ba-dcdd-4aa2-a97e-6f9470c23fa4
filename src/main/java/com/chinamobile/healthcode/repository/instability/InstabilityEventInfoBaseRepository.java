package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.InstabilityEventInfoBaseEntity;
import com.chinamobile.healthcode.model.subject.EventDescription;
import com.chinamobile.healthcode.repository.subject.EventRepository;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.*;

/**
 * 村（社）专项管理重点事件情况基础实体类
 */
public abstract class InstabilityEventInfoBaseRepository<T extends InstabilityEventInfoBaseEntity> extends InstabilityBaseRepository<T> {
    
    protected final EventRepository eventRepository;
    private static final Logger LOGGER = LoggerFactory.getLogger(InstabilityEventInfoBaseRepository.class);
    
    protected InstabilityEventInfoBaseRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Class<T> tClass,
            Validator validator,
            DefaultDepartmentRepository departmentRepository,
            DefaultResultParser resultParser,
            EventRepository eventRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass, validator, departmentRepository, resultParser);
        this.eventRepository = eventRepository;
    }

    @Override
    public Result<Void> validateBeforeSaveOrUpdate(T t) {
        Result<Void> validResult = new Result<>();

        Result<DefaultDepartment> deptResult = departmentRepository.get(t.getRegionId(), true);
        if (!deptResult.isOK() || deptResult.data.getLevel() < 3 || deptResult.data.getLevel() > 4) {
            validResult.setCode(Result.ENUM_ERROR.P, 9, new Object[]{"请选择村、社区或网格一级行政区域"});
        }

        return validResult;
    }
    
    @Override
    public Result<String> saveOrUpdate(T t, User actor) {
        Result<String> result = super.saveOrUpdate(t, actor);
        
        if (result.isOK()) {
            // 同步到重点事件
            try {
                String regionId = t.getRegionId();
                String overview = t.getOverview();
                // 查找是否存在相同regionId和overview的重点事件
                EventDescription existingEvent = stream(EventDescription.class)
                        .where(e -> e.getRegionId().equals(regionId) && e.getDescription().equals(overview))
                        .findFirst()
                        .orElse(null);

                if (existingEvent != null) {
                    // 更新现有事件
                    existingEvent.setRegionId(t.getRegionId());
                    existingEvent.setRegionFullName(departmentRepository.get(t.getRegionId(), true).data.getFullName());
                    existingEvent.setTitle(t.getTitle());
                    existingEvent.setType(t.getCategory());
                    existingEvent.setSubtype(t.getType());
                    existingEvent.setAddress(Optional.ofNullable(t.getAddress()).orElse(""));
                    existingEvent.setLng(t.getLngInGcj());
                    existingEvent.setLat(t.getLatInGcj());
                    existingEvent.setLngInWgs(t.getLngInWgs());
                    existingEvent.setLatInWgs(t.getLatInWgs());
                    existingEvent.setBeginTime(t.getIncidentTime());

                    eventRepository.update(existingEvent, actor.getId());
                } else {
                    // 创建新事件
                    EventDescription newEvent = new EventDescription();
                    newEvent.setRegionId(t.getRegionId());
                    newEvent.setRegionFullName(departmentRepository.get(t.getRegionId(), true).data.getFullName());
                    newEvent.setTitle(t.getTitle());
                    newEvent.setType(t.getCategory());
                    newEvent.setSubtype(t.getType());
                    newEvent.setDescription(t.getOverview());
                    newEvent.setAddress(Optional.ofNullable(t.getAddress()).orElse(""));
                    newEvent.setLng(t.getLngInGcj());
                    newEvent.setLat(t.getLatInGcj());
                    newEvent.setLngInWgs(t.getLngInWgs());
                    newEvent.setLatInWgs(t.getLatInWgs());
                    newEvent.setBeginTime(t.getIncidentTime());
                    newEvent.setDescription(t.getOverview());

                    eventRepository.add(newEvent, actor.getId());
                }
            } catch (Exception e) {
                LOGGER.error("同步到重点事件失败", e);
            }
        }
        
        return result;
    }
}
