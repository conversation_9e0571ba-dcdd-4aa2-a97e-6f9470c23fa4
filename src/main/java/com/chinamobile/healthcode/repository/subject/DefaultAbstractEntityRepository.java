package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.apache.commons.lang3.StringUtils;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.Arrays;
import java.util.Set;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 10/9/2023 10:36
 */
public class DefaultAbstractEntityRepository<T extends AbstractEntity> extends AbstractEntityRepository<T> {
    final Validator validator;

    public DefaultAbstractEntityRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, Validator validator) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.validator = validator;
    }

    /**
     * 因Hibernate默认使用Default group，使用额外的方法进行指定group检查
     *
     * @param item   检查的对象
     * @param groups 检查分组
     */
    public void validate(T item, Class<?>... groups) {
        Set<ConstraintViolation<T>> violations = validator.validate(item, groups);
        if (!violations.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (ConstraintViolation<T> constraintViolation : violations) {
                sb.append("\n");
                sb.append(constraintViolation.getMessage());
            }
            throw new ConstraintViolationException(sb.toString(), violations);
        }
    }

    public String multiSelStrToArrStr(String multiSelStr) {
        String arrStr;
        if (StringUtils.isBlank(multiSelStr)) {
            arrStr = null;
        } else {
            String[] multiSelArr = multiSelStr.split("[,，|]");
            StringJoiner stringJoiner = new StringJoiner(",", "[", "]");
            Arrays.stream(multiSelArr).map(s -> String.format("\"%s\"", s))
                    .forEach(stringJoiner::add);
            arrStr = stringJoiner.toString();
        }
        return arrStr;
    }

}
