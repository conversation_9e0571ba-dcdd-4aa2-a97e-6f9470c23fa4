package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.AliasToEntityOrderedMapResultTransformer;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.hibernate.Transaction;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "056")
public class PersonRepository extends DefaultAbstractEntityRepository<PersonDescription> implements ProjectRepository<PersonDescription> {

    public static final String ADMIN_ROLE = "专题管理员";

    final DepartmentRepository<Department> departmentRepository;
    final DefaultRoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;
    final ProfileRepository profileRepository;

    final DefaultResultParser resultParser;

    public PersonRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                            Validator validator,
                            DepartmentRepository departmentRepository,
                            DefaultRoleRepository roleRepository,
                            DictionaryRepository dictionaryRepository,
                            ProfileRepository profileRepository,
                            DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, PersonDescription.class, validator);

        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.profileRepository = profileRepository;

        this.resultParser = resultParser;
    }

    @Transactional(readOnly = true)
    public Result<PersonDescription> get(String id) {
        Result<PersonDescription> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{PersonDescription.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(PersonDescription.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{PersonDescription.class});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<PersonDescription> fuzzy(int count, int index, List<SortField> sortFields, String regionFullName, String name, PersonDescription.ENUM_TYPE type, PersonDescription.ENUM_STATUS status, User user) {
        JinqStream<PersonDescription> _query = stream(regionFullName, name, type, status, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime());
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<PersonDescription> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    public Result<String> save(PersonDescription item, String actorId) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<PersonDescription> _item = get(item.getId());
        if (!_item.isOK()) {
            String _identifier = item.getId();
            String _credentialType = item.getCredentialType();
            String _credentialNo = item.getCredentialNo();
            PersonDescription.ENUM_TYPE _type = item.getType();

            JinqStream<PersonDescription> _query = stream(PersonDescription.class)
                    .where(i -> _identifier == null || !i.getId().equals(_identifier));
            if (_query.where(i -> i.getCredentialType().equals(_credentialType)
                            && i.getCredentialNo().equals(_credentialNo)
                            && i.getType().equals(_type)
                    )
                    .findFirst().isPresent()) {
                _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
                return _id;
            }

            _item.data = new PersonDescription();
            _item.data.setCredentialType(item.getCredentialType());
            _item.data.setCredentialNo(item.getCredentialNo());

            _isNew = true;
        }

        copyProperties(item, _item.data, new String[]{"id", "credentialType", "credentialNo"});
        if (Objects.nonNull(_item.data.getLng()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLat(), _item.data.getLng());
            _item.data.setLatInWgs(_latLngInWgs[0]);
            _item.data.setLngInWgs(_latLngInWgs[1]);
        }

        modifyByType(_item.data);

        Result _success = validateByType(_item.data);
        if (!_success.isOK()) {
            return _success;
        }

        validate(_item.data, ValidationGroup.Insert.class, ValidationGroup.Update.class);
        _success = _isNew ? add(_item.data, actorId) : update(_item.data, actorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        } else {
            synchronizeToProfile(Collections.singletonList(_item.data.getCredentialNo()), actorId);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result remove(String id, User user) {
        Result _success = new Result();

        Result<PersonDescription> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位
        if (!departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .anyMatch(i -> i.getId().equals(_item.data.getRegionId()))) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        getCurrentSession().remove(_item.data);
        getCurrentSession().flush();
        synchronizeToProfile(Collections.singletonList(_item.data.getCredentialNo()), user.getId());

        return _success;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(File file, User actor) throws Exception {
        // 读取下级单位
        List<Department> _subordinates = departmentRepository.subordinates(actor.getDeptId(), null, null);

        // 读取部门
        List<Department> _departments = departmentRepository.query(null, null, null, true, null);

        List<PersonDescription> _items = stream(PersonDescription.class).toList();

        return readExcel((header, lines, results) -> {
            for (int i = 0; i < lines.size(); i++) {
                if (!results.get(i).isOK()) {
                    continue;
                }

                String[] _line = lines.get(i);

                PersonDescription _item = new PersonDescription();

                String _text = _line[header.get("姓名")];
                _item.setName(_text);

                _text = _line[header.get("性别")];
                _item.setIsMale(Objects.equals(_text, "男性"));

                _text = _line[header.get("人员类型")];
                _item.setPopulationType(_text);

                _text = _line[header.get("证件类型")];
                _item.setCredentialType(_text);

                _text = _line[header.get("证件号码")];
                _item.setCredentialNo(_text);

                _text = _line[header.get("联系方式")];
                _item.setContact(_text);

                String _name = _line[header.get("归属网格")];
                Department _region = _departments.stream()
                        .filter(j -> Objects.equals(_name, j.getFullName()))
                        .findFirst().orElse(null);
                if (_region == null) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 3);
                    results.set(i, _success);

                    continue;
                }

                // 非下级单位
                if (!_subordinates.stream().anyMatch(j -> Objects.equals(j.getId(), _region.getId()))) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 4);
                    results.set(i, _success);

                    continue;
                }
                _item.setRegionId(_region.getId());
                _item.setRegionFullName(_region.getFullName());

                _text = _line[header.get("现居住地")];
                _item.setAddress(_text);

                try {
                    _text = _line[header.get("人员类别")];
                    PersonDescription.ENUM_TYPE _type = PersonDescription.ENUM_TYPE.valueOf(_text);
                    _item.setType(_type);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 5);
                    results.set(i, _success);

                    continue;
                }

                try {
                    _text = _line[header.get("人员状态")];
                    PersonDescription.ENUM_STATUS _status = PersonDescription.ENUM_STATUS.valueOf(_text);
                    _item.setStatus(_status);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 6);
                    results.set(i, _success);

                    continue;
                }

                switch (_item.getType()) {
                    case 吸毒人员:
                        try {
                            _text = _line[header.get("最近一次尿检时间")];
                            Date _time = DateUtil.from(_text, "yyyy-MM-dd HH:mm:ss");
                            _item.setLastUrineTime(_time);
                        } catch (Throwable e) {
                            Result _success = new Result();
                            _success.setCode(Result.ENUM_ERROR.P, 7);
                            results.set(i, _success);

                            continue;
                        }

                        break;
                    case 社区矫正人员:
                        try {
                            _text = _line[header.get("矫正开始时间")];
                            Date _time = DateUtil.from(_text, "yyyy-MM-dd HH:mm:ss");
                            _item.setRectifyFrom(_time);
                        } catch (Throwable e) {
                            Result _success = new Result();
                            _success.setCode(Result.ENUM_ERROR.P, 8);
                            results.set(i, _success);

                            continue;
                        }

                        _text = _line[header.get("矫正期限")];
                        _item.setRectifyTimeLimit(_text);

                        break;
                    case 上访人员:
                        _text = _line[header.get("主要上访事由")];
                        _item.setPetition(_text);

                        _text = _line[header.get("是否签订《息诉罢访协议书》")];
                        boolean _is = Objects.equals(_text, "是");
                        _item.setArchiveAgreement(_is);

                        break;
                    case 严重精神障碍患者:
                        _text = _line[header.get("是否低保户")];
                        boolean _is2 = Objects.equals(_text, "是");
                        _item.setIsLowIncomeFamily(_is2);

                        _text = _line[header.get("监护人姓名")];
                        _item.setGuardianName(_text);

                        _text = _line[header.get("与患者关系")];
                        _item.setGuardianRelationship(_text);

                        _text = _line[header.get("监护人联系方式")];
                        _item.setGuardianContact(_text);

                        break;
                    case 重点未成年人:
                        _text = _line[header.get("人员等级")];
                        _item.setLevelOfConcern(_text);
                        _text = _line[header.get("人员分类（可同时存在多种类别）")];
                        _item.setCategoryOfMinor(multiSelStrToArrStr(_text));
                        _text = _line[header.get("学校")];
                        _item.setSchoolName(_text);
                        _text = _line[header.get("家庭情况(可同时存在多种类别)")];
                        _item.setFamilyStatus(multiSelStrToArrStr(_text));
                        _text = _line[header.get("未成年人监护人姓名")];
                        _item.setGuardianName(_text);
                        _text = _line[header.get("未成年人监护人联系方式")];
                        _item.setGuardianContact(_text);
                        _text = _line[header.get("未成年人监护人身份号码")];
                        _item.setGuardianCredentialNo(_text);
                        _text = _line[header.get("与未成年人关系")];
                        _item.setGuardianRelationship(_text);
                        _text = _line[header.get("未成年人监护人居住详址")];
                        _item.setAddress(_text);
                        _text = _line[header.get("帮扶手段（可同时存在多种类别)")];
                        _item.setWayToHelp(multiSelStrToArrStr(_text));
                        _text = _line[header.get("帮扶人姓名")];
                        _item.setHelperName(_text);
                        _text = _line[header.get("帮扶人联系方式")];
                        _item.setHelperContact(_text);
                        _text = _line[header.get("帮扶情况")];
                        _item.setHelpSituation(_text);
                        _text = _line[header.get("主责部门")];
                        _item.setResponsibleDepartment(_text);
                        _text = _line[header.get("主责人")];
                        _item.setResponsiblePersonName(_text);
                        _text = _line[header.get("主责人联系电话")];
                        _item.setResponsiblePersonContact(_text);
                        _text = _line[header.get("备注")];
                        _item.setMemo(_text);
                        break;
                }

                Result _success = validateByType(_item);
                if (!_success.isOK()) {
                    results.set(i, _success);

                    continue;
                }

                Transaction _transaction = getCurrentSession().beginTransaction();
                try {
                    PersonDescription _source = _items.stream()
                            .filter(j -> Objects.equals(_item.getCredentialType(), j.getCredentialType())
                                    && Objects.equals(_item.getCredentialNo(), j.getCredentialNo())
                                    && Objects.equals(_item.getType(), j.getType())
                            )
                            .findFirst().orElse(null);
                    if (_source == null) {
                        validate(_item, ValidationGroup.Insert.class);
                        add(_item, actor.getId());
                        synchronizeToProfile(Collections.singletonList(_item.getCredentialNo()), actor.getId());

                        _items.add(_item);
                    } else {
                        copyProperties(_item, _source, new String[]{"id"});
                        validate(_source, ValidationGroup.Update.class);
                        update(_source, actor.getId());
                        synchronizeToProfile(Collections.singletonList(_source.getCredentialNo()), actor.getId());
                    }

                    _transaction.commit();
                } catch (Throwable e) {
                    e.printStackTrace();

                    Result _error = resultParser.fromException(e, false);
                    if (_error == null) {
                        _error = new Result();
                        _error.setCode(Result.ENUM_ERROR.P, 12, new Object[]{e.getMessage()});
                    }
                    results.set(i, _error);

                    _transaction.rollback();
                }
            }
        }, file, 0, 3);
    }

    public JinqStream<PersonDescription> stream(String regionFullName, String name, PersonDescription.ENUM_TYPE type, PersonDescription.ENUM_STATUS status, User user) {
        JinqStream<PersonDescription> _query = stream(PersonDescription.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (type != null) {
            _query = _query.where(i -> type == i.getType());
        }

        if (status != null) {
            _query = _query.where(i -> status == i.getStatus());
        }

        return _query;
    }

    @Override
    public String getProjectType() {
        return "重点人员";
    }

    @Override
    public List<PersonDescription> fuzzyProjectDescription(String type, User user) {
        PersonDescription.ENUM_TYPE _enumType = Arrays.stream(PersonDescription.ENUM_TYPE.values())
                .filter(e -> e.name().equals(type))
                .findAny()
                .orElse(null);
        return _enumType != null
                ? stream(null, null, _enumType, null, user)
                .collect(Collectors.toList())
                : Collections.emptyList();
    }

    @Override
    public PersonDescription fromForm(String subType, Form form) {
        PersonDescription _desc;
        PersonDescription.ENUM_STATUS _enumStatus = Arrays.stream(PersonDescription.ENUM_STATUS.values())
                .filter(s -> s.name().equals(form.getMajorPersonState()))
                .findFirst()
                .orElse(PersonDescription.ENUM_STATUS.在管);
        PersonDescription.ENUM_TYPE _enumType = Arrays.stream(PersonDescription.ENUM_TYPE.values())
                .filter(e -> e.name().equals(subType))
                .findAny()
                .orElse(null);

        // 新增但证件信息已存在专题数据中，改为更新
        if (!StringUtils.hasLength(form.getRefId())
                && StringUtils.hasLength(form.getCredentialType()) && StringUtils.hasLength(form.getCredentialNo())) {
            JinqStream<PersonDescription> _query = stream(PersonDescription.class);
            PersonDescription desc = _query.where(p -> p.getCredentialType().equals(form.getCredentialType()))
                    .where(p -> p.getCredentialNo().equals(form.getCredentialNo()))
                    .where(p -> p.getType() == _enumType)
                    .findFirst()
                    .orElse(null);
            if (desc != null) {
                form.setRefId(desc.getId());
            }
        }

        if (StringUtils.hasLength(form.getRefId())) {
            // 初始化数据被更新
            Result<PersonDescription> _r = get(form.getRefId());
            if (_r.isOK()
                    // 专题记录未更新过
                    && (_r.data.getMaintainTime() == null
                    // 初始化的专项记录被更新后更新时间不应为null，避免异常
                    // 专项记录更新时间在专题记录更新时间之后
                    || (form.getMaintainTime() != null && _r.data.getMaintainTime().before(form.getMaintainTime())))) {
                _desc = _r.data;
                Optional.ofNullable(form.getPersonName()).ifPresent(_desc::setName);
                Optional.ofNullable(form.getSex()).ifPresent(_desc::setIsMale);
                Optional.ofNullable(form.getCredentialType()).ifPresent(_desc::setCredentialType);
                Optional.ofNullable(form.getCredentialNo()).ifPresent(_desc::setCredentialNo);
                Optional.ofNullable(form.getResidentialAddress()).ifPresent(_desc::setAddress);
                _desc.setStatus(_enumStatus);
                Optional.ofNullable(form.getLowIncomeFamily()).ifPresent(_desc::setIsLowIncomeFamily);
                Optional.ofNullable(form.getGuardianName()).ifPresent(_desc::setGuardianName);
                Optional.ofNullable(form.getGuardianRelationship()).ifPresent(_desc::setGuardianRelationship);
                Optional.ofNullable(form.getGuardianContact()).ifPresent(_desc::setGuardianContact);
                Optional.ofNullable(form.getRiskLevel()).ifPresent(_desc::setRiskLevel);
                Optional.ofNullable(form.getLastUrineTime()).ifPresent(_desc::setLastUrineTime);
                Optional.ofNullable(form.getPetition()).ifPresent(_desc::setPetition);
                Optional.ofNullable(form.getAchieveAgreement()).ifPresent(_desc::setArchiveAgreement);
                Optional.ofNullable(form.getRectifyFrom()).ifPresent(_desc::setRectifyFrom);
                Optional.ofNullable(form.getRectifyTimeLimit()).ifPresent(_desc::setRectifyTimeLimit);
                Optional.ofNullable(form.getContact()).ifPresent(_desc::setContact);
            } else {
                // 不更新
                _desc = null;
            }
        } else {
            // 新增
            if (_enumType == null) {
                _desc = null;
            } else {
                _desc = new PersonDescription();
                _desc.setType(_enumType);
                _desc.setName(Optional.ofNullable(form.getPersonName()).orElse(""));
                _desc.setIsMale(Optional.ofNullable(form.getSex()).orElse(false));
                _desc.setCredentialType(Optional.ofNullable(form.getCredentialType()).orElse(""));
                _desc.setCredentialNo(Optional.ofNullable(form.getCredentialNo()).orElse(""));
                _desc.setAddress(Optional.ofNullable(form.getResidentialAddress()).orElse(""));
                _desc.setStatus(_enumStatus);
                _desc.setIsLowIncomeFamily(form.getLowIncomeFamily());
                _desc.setGuardianName(form.getGuardianName());
                _desc.setGuardianRelationship(form.getGuardianRelationship());
                _desc.setGuardianContact(form.getGuardianContact());
                _desc.setRiskLevel(form.getRiskLevel());
                _desc.setLastUrineTime(form.getLastUrineTime());
                _desc.setPetition(form.getPetition());
                _desc.setArchiveAgreement(form.getAchieveAgreement());
                _desc.setRectifyFrom(form.getRectifyFrom());
                _desc.setRectifyTimeLimit(form.getRectifyTimeLimit());
                _desc.setContact(form.getContact());
            }
        }
        return _desc;
    }

    public Result<List<Pair<String, Long>>> myStats(User user) {
        Result<List<Pair<String, Long>>> _result = new Result<>();
        String _userId = user.getId();

        List<Pair<PersonDescription.ENUM_TYPE, Long>> _personCountList = stream(PersonDescription.class)
                .where(i -> i.getCreatorId().equals(_userId))
                .group(i -> i.getType(), (type, stream) -> stream.count())
                .toList();

        List<Pair<String, Long>> _countList = new ArrayList<>();
        long _profileCount = stream(Profile.class).where(i -> i.getCreatorId().equals(_userId))
                        .count();
        _countList.add(new Pair<>("社会面扫码", _profileCount));
        _countList.addAll(Arrays.stream(PersonDescription.ENUM_TYPE.values())
                .map(t ->
                        _personCountList.stream().filter(p -> p.getOne() == t).findFirst().map(p -> new Pair<>(p.getOne().name(), p.getTwo()))
                                .orElse(new Pair<>(t.name(), 0L))
                )
                .collect(Collectors.toList()));

        _result.data = _countList;
        return _result;
    }

    @Transactional(readOnly = true)
    public Tuple4<Long, Long, Long, List<String>> getInstabilityDifferenceWithDetails(String type, User user) {
        List<PersonDescription.ENUM_TYPE> typeList = Optional.ofNullable(type)
                .filter(org.springframework.util.StringUtils::hasLength)
                .map(PersonDescription.ENUM_TYPE::valueOf)
                .map(Collections::singletonList)
                .orElse(Arrays.asList(PersonDescription.ENUM_TYPE.values()));

        // 存在映射的类型
        String typeMappingStr = dictionaryRepository.getVal("重点人员", "村社区管理类型映射", true);
        JsonObject typeMapping = ConverterUtil.json2Object(typeMappingStr, JsonObject.class);
        typeList = typeList.stream()
                .filter(t -> typeMapping.has(t.name()))
                .collect(Collectors.toList());
//        _query.setResultTransformer(AliasToEntityOrderedMapResultTransformer.INSTANCE);

        String queryFields = typeList.stream().map(PersonDescription.ENUM_TYPE::getDisplayName)
                .map(n -> "`村社区管理-" + n + "`, `专题管理-" + n + "`")
                .collect(Collectors.joining(", "));

        // 从视图查询数据
        List<Object[]> resultList = getCurrentSession().createNativeQuery(
                        "SELECT " + queryFields + " FROM subject_instability_difference WHERE `村/社区` LIKE :regionFullName")
                .setParameter("regionFullName", ("默认".equals(user.getDeptFullName()) ? "" : user.getDeptFullName()) + "%")
                .getResultList();

        long villageCount = 0L;
        long subjectCount = 0L;
        List<String> details = new ArrayList<>();
        for (int i = 0; i < typeList.size(); i++) {
            long v = 0L, s = 0L;
            for (Object[] o : resultList) {
                v += Long.parseLong(o[i * 2].toString());
                s += Long.parseLong(o[i * 2 + 1].toString());
            }
            villageCount += v;
            subjectCount += s;
            long diff = v - s;
            details.add("上报" + typeList.get(i).getDisplayName() + v + "位，已建档" + s + "位，差异" + diff + "位");
        }
        return new Tuple4<>(villageCount, subjectCount, villageCount - subjectCount, details);
    }

    @Transactional(readOnly = true)
    public JsonObject queryOptions() {
        String _json = dictionaryRepository.getVal("重点人员", "类型", true);
        return Optional.ofNullable(_json).map(s -> ConverterUtil.json2Object(s, JsonObject.class)).orElse(new JsonObject());
    }

    public void synchronizeToProfile() {
        List<String> _credentialNoList = stream(PersonDescription.class).map(PersonDescription::getCredentialNo).collect(Collectors.toList());
        synchronizeToProfile(_credentialNoList, null);
    }

    void modifyByType(PersonDescription item) {
        switch (item.getType()) {
            case 吸毒人员:
                item.setRectifyFrom(null);
                item.setRectifyTimeLimit(null);
                item.setPetition(null);
                item.setArchiveAgreement(null);
                item.setGuardianName(null);
                item.setGuardianRelationship(null);
                item.setGuardianContact(null);
                item.setIsLowIncomeFamily(null);
                item.setLevelOfConcern(null);
                item.setCategoryOfMinor(null);
                item.setSchoolName(null);
                item.setFamilyStatus(null);
                item.setGuardianCredentialNo(null);
                item.setWayToHelp(null);
                item.setHelperName(null);
                item.setHelperContact(null);
                item.setHelpSituation(null);
                item.setResponsibleDepartment(null);
                item.setResponsiblePersonName(null);
                item.setResponsiblePersonContact(null);
                item.setMemo(null);
                break;
            case 社区矫正人员:
                item.setLastUrineTime(null);
                item.setPetition(null);
                item.setArchiveAgreement(null);
                item.setGuardianName(null);
                item.setGuardianRelationship(null);
                item.setGuardianContact(null);
                item.setIsLowIncomeFamily(null);
                item.setLevelOfConcern(null);
                item.setCategoryOfMinor(null);
                item.setSchoolName(null);
                item.setFamilyStatus(null);
                item.setGuardianCredentialNo(null);
                item.setWayToHelp(null);
                item.setHelperName(null);
                item.setHelperContact(null);
                item.setHelpSituation(null);
                item.setResponsibleDepartment(null);
                item.setResponsiblePersonName(null);
                item.setResponsiblePersonContact(null);
                item.setMemo(null);
                break;
            case 上访人员:
                item.setLastUrineTime(null);
                item.setRectifyFrom(null);
                item.setRectifyTimeLimit(null);
                item.setGuardianName(null);
                item.setGuardianRelationship(null);
                item.setGuardianContact(null);
                item.setIsLowIncomeFamily(null);
                item.setLevelOfConcern(null);
                item.setCategoryOfMinor(null);
                item.setSchoolName(null);
                item.setFamilyStatus(null);
                item.setGuardianCredentialNo(null);
                item.setWayToHelp(null);
                item.setHelperName(null);
                item.setHelperContact(null);
                item.setHelpSituation(null);
                item.setResponsibleDepartment(null);
                item.setResponsiblePersonName(null);
                item.setResponsiblePersonContact(null);
                item.setMemo(null);
                break;
            case 严重精神障碍患者:
                item.setLastUrineTime(null);
                item.setRectifyFrom(null);
                item.setRectifyTimeLimit(null);
                item.setPetition(null);
                item.setArchiveAgreement(null);
                item.setLevelOfConcern(null);
                item.setCategoryOfMinor(null);
                item.setSchoolName(null);
                item.setFamilyStatus(null);
                item.setGuardianCredentialNo(null);
                item.setWayToHelp(null);
                item.setHelperName(null);
                item.setHelperContact(null);
                item.setHelpSituation(null);
                item.setResponsibleDepartment(null);
                item.setResponsiblePersonName(null);
                item.setResponsiblePersonContact(null);
                item.setMemo(null);
                break;
            case 重点未成年人:
                item.setRectifyFrom(null);
                item.setRectifyTimeLimit(null);
                item.setPetition(null);
                item.setArchiveAgreement(null);
                item.setIsLowIncomeFamily(null);
                item.setLastUrineTime(null);
                break;
        }
    }

    Result validateByType(PersonDescription item) {
        Result _success = new Result();

        if (Arrays.stream(Profile.ENUM_CREDENTIAL_TYPE.values()).map(Enum::name).noneMatch(n -> n.equals(item.getCredentialType()))) {
            _success.setCode(Result.ENUM_ERROR.P, 9);
            return _success;
        }

        if (Profile.ENUM_CREDENTIAL_TYPE.身份证.name().equals(item.getCredentialType()) && (item.getCredentialNo() == null || item.getCredentialNo().length() != 18)) {
            _success.setCode(Result.ENUM_ERROR.P, 10);
            return _success;
        }

        try {
            switch (item.getType()) {
                case 严重精神障碍患者:
                    if (StringUtils.isEmpty(item.getContact()) || StringUtils.isEmpty(item.getGuardianName()) || StringUtils.isEmpty(item.getGuardianRelationship()) || StringUtils.isEmpty(item.getGuardianContact()) || item.getIsLowIncomeFamily() == null) {
                        _success.setCode(Result.ENUM_ERROR.P, 11);
                        return _success;
                    }
                    break;
                case 重点未成年人:
                    validate(item, ValidationGroup.SubjectPersonMinors.class);
                    break;
            }
        } catch (ConstraintViolationException e) {
            _success.setCode(Result.ENUM_ERROR.P, 12, new Object[]{e.getMessage()});
        }

        return _success;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void synchronizeToProfile(List<String> credentialNoList, String actorId) {
        // profile数据
        List<Profile> _profileList = stream(Profile.class).where(p -> JPQL.isInList(p.getCredentialNo(), credentialNoList))
                .collect(Collectors.toList());

        // 更新的数据
        List<PersonDescription> _personList = stream(PersonDescription.class)
                .where(p -> JPQL.isInList(p.getCredentialNo(), credentialNoList))
                .collect(Collectors.toList());

        // 删除的数据
        List<String> _deletedCredentialNoList = new ArrayList<>(credentialNoList);
        _deletedCredentialNoList.removeAll(_personList.stream()
                .map(PersonDescription::getCredentialNo)
                .distinct()
                .collect(Collectors.toList()));

        // 证件号码与重点人员类型标签映射
        Map<String, List<PersonDescription>> _credentialNoSubjectPersonMap = _personList.stream()
                .sorted(Comparator.comparing(PersonDescription::getCredentialNo)
                        .thenComparing(PersonDescription::getMaintainTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed()
                        .thenComparing(PersonDescription::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed())
                .collect(Collectors.groupingBy(PersonDescription::getCredentialNo));
        _credentialNoSubjectPersonMap.forEach((key, val) -> {
            _profileList.stream().filter(p -> p.getCredentialNo().equals(key)).findAny().ifPresent(profile -> {
                PersonDescription _person = val.get(0);
                profile.setName(_person.getName());
                profile.setMp(_person.getContact());
                profile.setSubdistrictId(_person.getRegionId());
                profile.setSubdistrictFullName(_person.getRegionFullName());
                profile.setAddress(_person.getAddress());
                String _subjectPersonTypes = val.stream()
                        .sorted(Comparator.comparing(PersonDescription::getType, Comparator.comparing(Enum::ordinal)))
                        .map(PersonDescription::getType)
                        .map(PersonDescription.ENUM_TYPE::getDisplayName)
                        .collect(Collectors.joining("，"));
                profile.setSubjectPerson(true);
                profile.setSubjectPersonTypes(_subjectPersonTypes);
                profileRepository.update(profile, actorId);
            });
        });

        _deletedCredentialNoList.forEach(cn -> {
            _profileList.stream().filter(p -> p.getCredentialNo().equals(cn)).findAny().ifPresent(p -> {
                p.setSubjectPerson(false);
                p.setSubjectPersonTypes(null);
                profileRepository.update(p, actorId);
            });
        });
    }

}
