package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.subject.EventDescription;
import com.chinamobile.healthcode.model.subject.Participant;
import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.hibernate.Transaction;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.io.File;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "054")
public class EventRepository extends DefaultAbstractEntityRepository<EventDescription> implements ProjectRepository<EventDescription> {

    public static final String ADMIN_ROLE = "专题管理员";
    public static final String TYPE_DICT_NAME = "重点事件-类别";

    final EventParticipantRepository eventParticipantRepository;
    final DepartmentRepository<Department> departmentRepository;
    final DefaultRoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;

    final DefaultResultParser resultParser;

    public EventRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, Validator validator, EventParticipantRepository eventParticipantRepository, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, DictionaryRepository dictionaryRepository, DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, EventDescription.class, validator);

        this.eventParticipantRepository = eventParticipantRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.resultParser = resultParser;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<EventDescription> sort(JinqStream<EventDescription> query, BiFunction<JinqStream<EventDescription>, JinqStream.CollectComparable<EventDescription, V>, JinqStream<EventDescription>> compare, String field) {
        switch (field) {
            case "title":
                query = compare.apply(query, i -> (V) i.getTitle());
                break;
            case "type":
                query = compare.apply(query, i -> (V) i.getType());
                break;
            case "beginTime":
                query = compare.apply(query, i -> (V) i.getBeginTime());
                break;
        }

        return query;
    }

    @Transactional(readOnly = true)
    public Result<EventDescription> get(String id) {
        Result<EventDescription> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{EventDescription.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(EventDescription.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{EventDescription.class.getSimpleName()});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<EventDescription> fuzzy(int count,
                                               int index,
                                               List<SortField> sortFields,
                                               String regionFullName,
                                               String type,
                                               String subtype,
                                               String title,
                                               String credentialNo,
                                               String name,
                                               String description,
                                               Boolean finished,
                                               Date beginTime,
                                               Date endTime,
                                               String address,
                                               User user) {
        JinqStream<EventDescription> _query = stream(regionFullName, type, subtype, title, credentialNo, name, description, finished, beginTime, endTime, address, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime());
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<EventDescription> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    public Result<String> save(EventDescription item, User user) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;
        Result<EventDescription> _item = get(item.getId());
        if (!_item.isOK()) {
            _item.data = new EventDescription();

            _isNew = true;
        }

        Result<Department> _region = departmentRepository.get(item.getRegionId(), true);
        if (!_region.isOK()) {
            return _id.pack(_region);
        }
        _item.data.setRegionId(_region.data.getId());
        _item.data.setRegionFullName(_region.data.getFullName());

        copyProperties(item, _item.data, new String[]{"id", "regionId", "regionFullName"});
        if (Objects.nonNull(_item.data.getLng()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLat(), _item.data.getLng());
            _item.data.setLatInWgs(_latLngInWgs[0]);
            _item.data.setLngInWgs(_latLngInWgs[1]);
        }

        Result _success = _isNew ? add(_item.data, user.getId()) : update(_item.data, user.getId());
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result remove(String id, User user) {
        Result _success = new Result();

        Result<EventDescription> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位
        if (!departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .anyMatch(i -> i.getId().equals(_item.data.getRegionId()))) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(File file, User actor) throws Exception {
        // 读取下级单位
        List<Department> _subordinates = departmentRepository.subordinates(actor.getDeptId(), null, null);

        // 读取部门
        List<Department> _departments = departmentRepository.query(null, null, null, true, null);

        List<String> _types = new ArrayList<>();
        List<String> _subtypes = new ArrayList<>();

        JsonObject _catalog = types();
        if (_catalog != null) {
            for (Map.Entry<String, JsonElement> i : _catalog.entrySet()) {
                _types.add(i.getKey());

                Set<String> _keys = i.getValue().getAsJsonObject().keySet();
                if (!CollectionUtils.isEmpty(_keys)) {
                    _subtypes.addAll(_keys);
                }
            }
        }

        return readExcel((header, lines, results) -> {
            for (int i = 0; i < lines.size(); i++) {
                if (!results.get(i).isOK()) {
                    continue;
                }

                String[] _line = lines.get(i);

                EventDescription _item = new EventDescription();

                String _text = _line[header.get("标题")];
                _item.setTitle(_text);

                String _name = _line[header.get("归属网格")];
                Department _region = _departments.stream()
                        .filter(j -> Objects.equals(_name, j.getFullName()))
                        .findFirst().orElse(null);
                if (_region == null) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 3);
                    results.set(i, _success);

                    continue;
                }

                // 非下级单位
                if (!_subordinates.stream().anyMatch(j -> Objects.equals(j.getId(), _region.getId()))) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 4);
                    results.set(i, _success);

                    continue;
                }
                _item.setRegionId(_region.getId());
                _item.setRegionFullName(_region.getFullName());

                _text = _line[header.get("事件分类")];
                if (StringUtils.isEmpty(_text) || !_types.contains(_text)) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 5);
                    results.set(i, _success);

                    continue;
                }
                _item.setType(_text);

                _text = _line[header.get("事件类型")];
                if (StringUtils.isEmpty(_text) || !_subtypes.contains(_text)) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 5);
                    results.set(i, _success);

                    continue;
                }
                _item.setSubtype(_text);

                _text = _line[header.get("事件概述")];
                _item.setDescription(_text);

                _text = _line[header.get("事发地")];
                _item.setAddress(_text);

                try {
                    _text = _line[header.get("事发地经度")];
                    _item.setLng(Double.valueOf(_text));
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 6);
                    results.set(i, _success);

                    continue;
                }

                try {
                    _text = _line[header.get("事发地纬度")];
                    _item.setLat(Double.valueOf(_text));
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 7);
                    results.set(i, _success);

                    continue;
                }

                if (Objects.nonNull(_item.getLng()) && Objects.nonNull(_item.getLat())) {
                    double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.getLat(), _item.getLng());
                    _item.setLatInWgs(_latLngInWgs[0]);
                    _item.setLngInWgs(_latLngInWgs[1]);
                }

                try {
                    _text = _line[header.get("事发时间")];
                    _item.setBeginTime(DateUtil.from(_text, "yyyy-MM-dd HH:mm:ss"));
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 8);
                    results.set(i, _success);

                    continue;
                }

                Transaction _transaction = getCurrentSession().beginTransaction();
                try {
                    validate(_item, ValidationGroup.Insert.class);
                    add(_item, actor.getId());

                    _transaction.commit();
                } catch (Throwable e) {
                    e.printStackTrace();

                    Result _error = resultParser.fromException(e, false);
                    if (_error == null) {
                        _error = new Result();
                        _error.setCode(Result.ENUM_ERROR.P, 9, new Object[]{e.getMessage()});
                    }
                    results.set(i, _error);

                    _transaction.rollback();
                }
            }
        }, file, 0, 1);
    }

    public JinqStream<EventDescription> stream(String regionFullName,
                                               String type,
                                               String subtype,
                                               String title,
                                               String credentialNo,
                                               String name,
                                               String description,
                                               Boolean finished,
                                               Date beginTime,
                                               Date endTime,
                                               String address,
                                               User user) {
        JinqStream<EventDescription> _query = stream(EventDescription.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(type)) {
            _query = _query.where(i -> type.equals(i.getType()));
        }

        if (StringUtils.hasLength(subtype)) {
            _query = _query.where(i -> subtype.equals(i.getSubtype()));
        }

        if (StringUtils.hasLength(title)) {
            _query = _query.where(i -> i.getTitle() != null && i.getTitle().contains(title));
        }

        if (StringUtils.hasLength(credentialNo) || StringUtils.hasLength(name)) {
            _query = _query.leftOuterJoin((i, source) -> source.stream(Participant.class), (i, participant) -> i.getId().equals(participant.getEventId()))
                    .where(i -> i.getTwo() != null)
                    .where(i -> credentialNo == null || credentialNo.equals("") || i.getTwo().getCredentialNo().contains(credentialNo))
                    .where(i -> name == null || name.equals("") || i.getTwo().getName().contains(name))
                    .select(i -> i.getOne());
        }

        if (StringUtils.hasLength(description)) {
            _query = _query.where(i -> i.getDescription().contains(description));
        }

        if (finished != null) {
            _query = _query.where(i -> finished == i.getFinished());
        }

        if (beginTime != null) {
            _query = _query.where(i -> !i.getBeginTime().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> i.getBeginTime().before(endTime));
        }

        if (address != null) {
            _query = _query.where(i -> i.getAddress().contains(address));
        }

        return _query;
    }

    @Transactional(readOnly = true)
    public JsonObject types() {
        String _json = dictionaryRepository.getVal("重点事件", "类型", true);
        return StringUtils.hasLength(_json) ? ConverterUtil.json2Object(_json, JsonObject.class) : null;
    }

    @Override
    public String getProjectType() {
        return "重点事件";
    }

    @Override
    public List<EventDescription> fuzzyProjectDescription(String type, User user) {
        return StringUtils.hasLength(type) ? stream(
                null,
                null,
                type,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                user)
                .collect(Collectors.toList())
                : Collections.emptyList();
    }

    @Override
    public EventDescription fromForm(String subType, Form form) {
        EventDescription _desc;
        if (StringUtils.hasLength(form.getRefId())) {
            // 初始化数据被更新
            Result<EventDescription> _r = get(form.getRefId());
            if (_r.isOK()
                    // 专题记录未更新过
                    && (_r.data.getMaintainTime() == null
                    // 初始化的专项记录被更新后更新时间不应为null，避免异常
                    // 专项记录更新时间在专题记录更新时间之后
                    || (form.getMaintainTime() != null && _r.data.getMaintainTime().before(form.getMaintainTime())))) {
                _desc = _r.data;
                Optional.ofNullable(form.getEventName()).ifPresent(_desc::setTitle);
                Optional.ofNullable(form.getEventAddress()).ifPresent(_desc::setAddress);
                Optional.ofNullable(form.getEventDescription()).ifPresent(_desc::setDescription);
            } else {
                // 不更新
                _desc = null;
            }
        } else {
            // 新增记录
            _desc = new EventDescription();
            _desc.setSubtype(Optional.ofNullable(subType).orElse(""));
            _desc.setTitle(Optional.ofNullable(form.getEventName()).orElse(""));
            _desc.setAddress(Optional.ofNullable(form.getEventAddress()).orElse(""));
            _desc.setDescription(Optional.ofNullable(form.getEventDescription()).orElse(""));
            _desc.setFinished(false);
        }

        return _desc;
    }

    public Result<List<Pair<String, Long>>> myStats(User user) {
        Result<List<Pair<String, Long>>> _result = new Result<>();
        String _userId = user.getId();

        List<Pair<String, Long>> _eventCountList = stream(EventDescription.class)
                .where(e -> e.getCreatorId().equals(_userId))
                .group(e -> new Pair<>(e.getType(), e.getSubtype()), (e, stream) -> stream.count())
                .map(p -> new Pair<>(String.join("-", p.getOne().getOne(), p.getOne().getTwo()), p.getTwo()))
                .collect(Collectors.toList());

        List<String> _typeList = types().entrySet().stream()
                .map(typeEntry -> typeEntry.getValue().getAsJsonObject().entrySet().stream()
                        .map(subTypeEntry -> String.join("-", typeEntry.getKey(), subTypeEntry.getKey()))
                        .collect(Collectors.toList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());

        List<Pair<String, Long>> _countList = _typeList.stream()
                .map(t -> _eventCountList.stream()
                        .filter(p -> p.getOne().equals(t))
                        .findFirst()
                        .orElse(new Pair<>(t, 0L)))
                .collect(Collectors.toList());

        _result.data = _countList;
        return _result;
    }

    @Transactional(readOnly = true)
    public Tuple4<Long, Long, Long, List<String>> getInstabilityDifference(String type, User user) {
        String typeMappingJson = dictionaryRepository.getVal("重点事件", "村社区管理类型映射", true);
        JsonObject typeMapping = ConverterUtil.json2Object(typeMappingJson, JsonObject.class);
        List<String> typeList = new ArrayList<>();
        if (StringUtils.hasLength(type)) {
//            typeList = Collections.singletonList(typeMapping.get(type).getAsString());
            typeList = Collections.singletonList(type);
        } else {
//            typeList = typeMapping.keySet().stream().map(typeMapping::get).map(JsonElement::getAsString).collect(Collectors.toList());
            typeList = new ArrayList<>(typeMapping.keySet());
        }
        // 只保留有映射的类型
        typeList = typeList.stream().filter(typeMapping::has).collect(Collectors.toList());
        String queryFields = typeList.stream()
                .map(n -> "`村社区管理-" + n + "`, `专题管理-" + n + "`")
                .collect(Collectors.joining(", "));

        // 从视图查询数据
        List<Object[]> resultList = getCurrentSession().createNativeQuery(
                        "SELECT " + queryFields + " FROM subject_instability_difference WHERE `村/社区` LIKE :regionFullName")
                .setParameter("regionFullName", ("默认".equals(user.getDeptFullName()) ? "" : user.getDeptFullName()) + "%")
                .getResultList();

        long villageCount = 0L;
        long subjectCount = 0L;
        List<String> details = new ArrayList<>();
        for (int i = 0; i < typeList.size(); i++) {
            long v = 0L, s = 0L;
            for (Object[] o : resultList) {
                v += Long.parseLong(o[i * 2].toString());
                s += Long.parseLong(o[i * 2 + 1].toString());
            }
            villageCount += v;
            subjectCount += s;
            long diff = v - s;
            String typeName = typeList.get(i);
            details.add("上报" + typeName + v + "件，已建档" + s + "件，差异" + diff + "件");
        }
        return new Tuple4<>(villageCount, subjectCount, villageCount - subjectCount, details);
    }
}
