package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.subject.PlaceDescription;
import com.chinamobile.healthcode.model.subject.PlacePersonDescription;
import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.calcite.util.ImmutableNullableList;
import org.hibernate.Transaction;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5/22/2023 8:44
 */
@Repository
@ErrorCode(module = "058")
public class PlaceRepository extends DefaultAbstractEntityRepository<PlaceDescription> implements ProjectRepository<PlaceDescription> {

    public static final String ADMIN_ROLE = "专题管理员";
    public static final String TYPE_DICT_NAME = "重点场所-类别";
    public static final List<String> PLACE_TYPE_REQUIRE_POPULATION = ImmutableNullableList.of("出租屋", "公寓", "民宿", "酒店");
    private final PlacePersonRepository placePersonRepository;

    private final DepartmentRepository<Department> departmentRepository;

    private final DefaultRoleRepository roleRepository;

    private final DictionaryRepository dictionaryRepository;

    private final ProfileRepository profileRepository;

    private final AbstractMediaRepository mediaRepository;

    private final DefaultResultParser resultParser;

    public PlaceRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                           @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJpaStreamProvider,
                           Validator validator,
                           PlacePersonRepository placePersonRepository,
                           DepartmentRepository<Department> departmentRepository,
                           DefaultRoleRepository roleRepository,
                           DictionaryRepository dictionaryRepository,
                           ProfileRepository profileRepository,
                           AbstractMediaRepository mediaRepository,
                           DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJpaStreamProvider, PlaceDescription.class, validator);
        this.placePersonRepository = placePersonRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.profileRepository = profileRepository;
        this.mediaRepository = mediaRepository;
        this.resultParser = resultParser;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public Result<PlaceDescription> get(String id) {
        Result<PlaceDescription> _item = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{PlaceDescription.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(PlaceDescription.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{PlaceDescription.class});
        }

        convertDictionaryFields(_item.data);

        return _item;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public Result<PlaceDescription> getWithPersons(String id) {
        Result<PlaceDescription> _item = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{PlaceDescription.class.getSimpleName()});
            return _item;
        }

        List<Pair<PlaceDescription, PlacePersonDescription>> _relations = stream(PlaceDescription.class).where(i -> id.equals(i.getId()))
                .leftOuterJoin((i, session) -> session.stream(PlacePersonDescription.class), (i, person) -> i.getId().equals(person.getPlaceId()))
                .toList();

        if (CollectionUtils.isEmpty(_relations)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _item;
        }

        _item.data = _relations.stream().map(Pair::getOne)
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _item;
        }

        List<PlacePersonDescription> _persons = _relations.stream()
                .filter(i -> i.getTwo() != null)
                .map(Pair::getTwo)
                .sorted(Comparator.comparing(AbstractEntity::getCreateTime))
                .collect(Collectors.toList());
        _item.data.setPersons(_persons);

        convertDictionaryFields(_item.data);
        parseAttachments(Collections.singletonList(_item.data), false);

        return _item;
    }

    /**
     * 页面新增修改
     *
     * @param item 输入数据
     * @param actorId 当前用户id
     * @return 场所id
     */
//    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> saveOrUpdate(PlaceDescription item, String actorId) {
        Result<String> _idResult = new Result<>();

        String _id = item.getId();
        String _placeName = item.getPlaceName();
        String _regionId = item.getRegionId();

        boolean _exists;
        if (StringUtils.hasLength(_id)) {
            _exists = stream(PlaceDescription.class)
                    .where(i -> i.getPlaceName().equals(_placeName)
                            && i.getRegionId().equals(_regionId)
                            && !i.getId().equals(_id))
                    .findAny()
                    .isPresent();
        } else {
            _exists = stream(PlaceDescription.class)
                    .where(i -> i.getPlaceName().equals(_placeName)
                            && i.getRegionId().equals(_regionId))
                    .findAny()
                    .isPresent();
        }

        if (_exists) {
            _idResult.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _idResult;
        }

        Result<PlaceDescription> _item = get(item.getId());

        boolean _newAdded = !_item.isOK();
        if (_newAdded) {
            _item.data = new PlaceDescription();
        }

        copyProperties(item, _item.data, _newAdded ? new String[]{"id"} : new String[]{"id", "credentialNo", "attachmentIdsJSON"});
        if (Objects.nonNull(_item.data.getLng()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLat(), _item.data.getLng());
            _item.data.setLatInWgs(_latLngInWgs[0]);
            _item.data.setLngInWgs(_latLngInWgs[1]);
        }

//        Transaction _transaction = getCurrentSession().beginTransaction();

        Result<?> _success = _newAdded ? add(_item.data, actorId) : update(_item.data, actorId);
        if (!_success.isOK()) {
//            _transaction.rollback();
            return _idResult.pack(_success);
        }

        _idResult.data = _item.data.getId();
        if (!CollectionUtils.isEmpty(item.getPersons())) {
            Result<List<PlacePersonDescription>> _result = placePersonRepository.save(_idResult.data, item.getPersons(), actorId);
            if (!_result.isOK()) {
//                _transaction.rollback();
                return _idResult.pack(_result);
            }

//            item.getPersons().stream().forEach(p -> {
//                Result<Profile> _profileResult = profileRepository.getBriefByCredentialNo(p.getCredentialNo());
//                if (!_profileResult.isOK()) {
//                    profileRepository.add(p.toProfile(), actorId);
//                }
//            });
        }
//        _transaction.commit();

        return _idResult;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public PagingItems<PlaceDescription> fuzzy(int count, int index, List<SortField> sortFields, String regionFullName,
                                               String placeName, String placeTypeValue, User user) {
        JinqStream<PlaceDescription> _query = stream(regionFullName, placeName, placeTypeValue, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(AbstractEntity::getCreateTime);
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<PlaceDescription> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        if (_page.items != null) {
            _page.items.forEach(this::convertDictionaryFields);
        }

        return _page;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<PlaceDescription> remove(String id, User user) {
        Result<PlaceDescription> _success = new Result<>();

        Result<PlaceDescription> _item = get(id);
        if (!_item.isOK()) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _success;
        }

        // 数据权限
        if (departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .noneMatch(i -> i.getId().equals(_item.data.getRegionId()))) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        Transaction _transaction = getCurrentSession().beginTransaction();
        try {
            getCurrentSession().remove(_item.data);

            Result<Void> _t = placePersonRepository.removeByPlaceId(id);
            if (!_t.isOK()) {
                _transaction.rollback();
                _success.pack(_t);
            } else {
                _transaction.commit();
            }
        } catch (Exception e) {
            _transaction.rollback();
        }

        return _success;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(File file, User actor) throws Exception {
        // 读取下级单位
        List<Department> _subordinates = departmentRepository.subordinates(actor.getDeptId(), null, null);

        // 读取部门
        List<Department> _departments = departmentRepository.query(null, null, null, true, null);

        List<Dictionary> _placeTypeDic = dictionaryRepository.fuzzy(TYPE_DICT_NAME, null, true, null);
        List<Dictionary> _subjectCategoryDic = dictionaryRepository.fuzzy("金凤码-特殊人群", null, true, null);
        List<Dictionary> _occupancyStateDic = dictionaryRepository.fuzzy("重点场所-人员状态", null, true, null);

        Map<String, String> _importedPlaceMap = new HashMap<>(2);

        return readExcel((header, lines, results) -> {
            for (int i = 0; i < lines.size(); i++) {
                if (!results.get(i).isOK()) {
                    continue;
                }

                Result<Void> _lineResult = new Result<>();
                String _colName = "";

//                Transaction _transaction = getCurrentSession().beginTransaction();
                try {
                    String[] _line = lines.get(i);

                    if (org.apache.commons.lang3.StringUtils.isAllBlank(_line)) {
                        continue;
                    }

                    String _regionFullName = getColValue(header, _line, "所属网格", true);
                    String _placeName = getColValue(header, _line, "场所名称（全称）", true);
                    String _tmpPlaceKey = String.join(",", _regionFullName, _placeName);
                    String _tmpPlaceId = _importedPlaceMap.get(_tmpPlaceKey);

                    // 同一个导入文件未出现过该场所
                    if (!_importedPlaceMap.containsKey(_tmpPlaceKey)) {
                        PlaceDescription _newItem = new PlaceDescription();

                        // 行政区域不存在
                        Department _region = _departments.stream()
                                .filter(d -> Objects.equals(_regionFullName, d.getFullName()))
                                .findFirst()
                                .orElseThrow(() -> new IllegalArgumentException("所属网格不存在"));

                        // 非下级单位
                        if (!_subordinates.stream().anyMatch(s -> Objects.equals(s.getId(), _region.getId()))) {
                            _lineResult.setCode(Result.ENUM_ERROR.P, 4);
                            results.set(i, _lineResult);
                            continue;
                        }
                        _newItem.setRegionId(_region.getId());
                        _newItem.setRegionFullName(_region.getFullName());

                        String _colValue = getColValue(header, _line, "场所类别", true);
                        Double _colDoubleValue;
                        String _placeTypeName = _colValue;
                        String _placeTypeValue = _placeTypeDic.stream()
                                .filter(d -> Objects.equals(d.getName(), _placeTypeName))
                                .map(Dictionary::getVal)
                                .findFirst()
                                .orElseThrow(() -> new IllegalArgumentException("无效的场所类别"));
                        _newItem.setPlaceTypeValue(_placeTypeValue);

                        _colValue = getColValue(header, _line, "场所名称（全称）", true);
                        _newItem.setPlaceName(_colValue);

                        _colValue = getColValue(header, _line, "负责人姓名", true);
                        _newItem.setPersonInCharge(_colValue);

                        _colValue = getColValue(header, _line, "身份证号码", true);
                        String _regex = "\\d{17}[\\d|x|X]|\\d{15}";
                        if (!_colValue.matches(_regex)) {
                            throw new IllegalArgumentException("无效的身份证号码");
                        }
                        _newItem.setCredentialNo(_colValue);

                        _colValue = getColValue(header, _line, "联系电话", true);
                        if (_colValue.length() > 11) {
                            throw new IllegalArgumentException("联系电话不能超过11位");
                        }
                        _newItem.setContact(_colValue);

                        _colName = "场所常住人口数";
                        _colDoubleValue = getColDoubleValue(header, _line, _colName, PLACE_TYPE_REQUIRE_POPULATION.contains(_placeTypeName));
                        _newItem.setPermanentPopulation(_colDoubleValue.intValue());

                        _colName = "外来人口数";
                        _colDoubleValue = getColDoubleValue(header, _line, _colName, PLACE_TYPE_REQUIRE_POPULATION.contains(_placeTypeName));
                        _newItem.setExternalPopulation(_colDoubleValue.intValue());

                        _colValue = getColValue(header, _line, "场所详细地址", true);
                        _newItem.setAddress(_colValue);

                        _colName = "国测局经度";
                        _colDoubleValue = getColDoubleValue(header, _line, _colName, false);
                        _newItem.setLng(_colDoubleValue);

                        _colName = "国测局纬度";
                        _colDoubleValue = getColDoubleValue(header, _line, _colName, false);
                        _newItem.setLat(_colDoubleValue);

                        if (Objects.nonNull(_newItem.getLng()) && Objects.nonNull(_newItem.getLat())) {
                            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_newItem.getLat(), _newItem.getLng());
                            _newItem.setLatInWgs(_latLngInWgs[0]);
                            _newItem.setLngInWgs(_latLngInWgs[1]);
                        }

                        _colName = "占地面积";
                        _colDoubleValue = getColDoubleValue(header, _line, _colName, false);
                        _newItem.setArea(_colDoubleValue);

                        _colValue = getColValue(header, _line, "备注", false);
                        _newItem.setRemarks(_colValue);

                        Result<?> _executeResult;
                        PlaceDescription _existingItem = stream(PlaceDescription.class)
                                .where(p -> Objects.equals(p.getPlaceName(), _placeName)
                                        && Objects.equals(p.getRegionFullName(), _regionFullName))
                                .findFirst()
                                .orElse(null);
                        if (Objects.isNull(_existingItem)) {
                            validate(_newItem, ValidationGroup.Insert.class);
                            _executeResult = add(_newItem, actor.getId());
                            _tmpPlaceId = _newItem.getId();
                        } else {
                            copyProperties(_newItem, _existingItem, new String[]{"id"});
                            validate(_existingItem, ValidationGroup.Update.class);
                            _executeResult = update(_existingItem, actor.getId());
                            _tmpPlaceId = _existingItem.getId();
                        }
                        if (!_executeResult.isOK()) {
                            _lineResult.pack(_executeResult);
                            results.set(i, _lineResult);
                            _importedPlaceMap.putIfAbsent(_tmpPlaceKey, null);
//                            _transaction.rollback();
                            continue;
                        } else {
                            _importedPlaceMap.putIfAbsent(_tmpPlaceKey, _tmpPlaceId);
                        }
                    }

                    String _personRegionFullName = getColValue(header, _line, "人员所属网格", false);
                    if (StringUtils.hasLength(_tmpPlaceId) && StringUtils.hasLength(_personRegionFullName)) {
                        PlacePersonDescription _newItem = new PlacePersonDescription();
                        _newItem.setPlaceId(_tmpPlaceId);

                        Department _region = _departments.stream()
                                .filter(d -> Objects.equals(_personRegionFullName, d.getFullName()))
                                .findFirst()
                                .orElseThrow(() -> new IllegalArgumentException("人员所属网格不存在"));

                        _newItem.setRegionId(_region.getId());
                        _newItem.setRegionFullName(_region.getFullName());

                        String _colValue = getColValue(header, _line, "姓名", true);
                        _newItem.setName(_colValue);

                        _colValue = getColValue(header, _line, "人员身份证号码", true);
                        _newItem.setCredentialNo(_colValue);

                        _colValue = getColValue(header, _line, "人员联系电话", true);
                        _newItem.setContact(_colValue);

                        _colValue = getColValue(header, _line, "户籍地址", true);
                        _newItem.setHkLocation(_colValue);

                        _colValue = getColValue(header, _line, "现居住地址", true);
                        _newItem.setAddress(_colValue);

                        _colValue = getColValue(header, _line, "从业/学习情况", true);
                        _newItem.setCareerSituation(_colValue);

                        _colValue = getColValue(header, _line, "现工作/学习地点", true);
                        _newItem.setWorkplace(_colValue);

                        _colValue = getColValue(header, _line, "重点人群类别", false);
                        if (StringUtils.hasLength(_colValue)) {
                            String[] _subjectCategoryNameArray = _colValue.split("[,，、;；|]");
                            String _subjectCategoryValues = Arrays.stream(_subjectCategoryNameArray)
                                    .map(n -> _subjectCategoryDic.stream()
                                            .filter(d -> Objects.equals(d.getName(), n))
                                            .map(Dictionary::getVal)
                                            .findFirst()
                                            .orElse("")
                                    )
                                    .filter(StringUtils::hasLength)
                                    .collect(Collectors.joining(","));
                            _newItem.setSubjectCategoryValue(_subjectCategoryValues);
                        } else {
                            _newItem.setSubjectCategoryValue("");
                        }

                        _colValue = getColValue(header, _line, "外出情况", false);
                        _newItem.setMigrantSituation(_colValue);

                        _colValue = getColValue(header, _line, "人员状态", true);
                        String _occupancyStateName = _colValue;
                        String _occupancyStateValue = _occupancyStateDic.stream()
                                .filter(d -> Objects.equals(d.getName(), _occupancyStateName))
                                .map(Dictionary::getVal)
                                .findFirst()
                                .orElseThrow(() -> new IllegalArgumentException("无效的人员状态"));
                        _newItem.setOccupancyStateValue(_occupancyStateValue);

                        _colValue = getColValue(header, _line, "人员备注", false);
                        _newItem.setRemarks(_colValue);

                        String _placeId = _newItem.getPlaceId();
                        String _credentialNo = _newItem.getCredentialNo();
                        PlacePersonDescription _existingItem = stream(PlacePersonDescription.class)
                                .where(p -> Objects.equals(p.getPlaceId(), _placeId)
                                        && Objects.equals(p.getCredentialNo(), _credentialNo))
                                .findFirst()
                                .orElse(null);
                        Result<Profile> _existingProfileResult = profileRepository.getBriefByCredentialNo(_newItem.getCredentialNo());
                        if (_existingProfileResult.isOK()) {
                            _newItem.setProfileId(_existingProfileResult.data.getId());
                        } else {
                            Profile _newProfile = _newItem.toProfile();
                            profileRepository.add(_newProfile, actor);
                            _newItem.setProfileId(_newProfile.getId());
                        }

                        Result<?> _executeResult;
                        if (Objects.isNull(_existingItem)) {
                            _executeResult = placePersonRepository.add(_newItem, actor.getId());
                        } else {
                            placePersonRepository.copyProperties(_newItem, _existingItem, new String[]{"id"});
                            _executeResult = placePersonRepository.update(_existingItem, actor.getId());
                        }
                        if (!_executeResult.isOK()) {
                            _lineResult.pack(_executeResult);
                            results.set(i, _lineResult);
//                            _transaction.rollback();
                            continue;
                        }
                    }
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                    _lineResult.setCode(Result.ENUM_ERROR.P, 5, new String[]{e.getMessage()});
                    results.set(i, _lineResult);
//                    _transaction.rollback();
                    continue;
                } catch (Exception e) {
                    e.printStackTrace();
                    Result<?> _error = resultParser.fromException(e, false);
                    if (_error == null) {
                        _error = new Result<>();
                        _error.setCode(Result.ENUM_ERROR.P, 5, new Object[]{e.getMessage()});
                    }
                    results.set(i, _error);
//                    _transaction.rollback();
                    continue;
                }
//                _transaction.commit();
            }
        }, file, 0, 1);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public JinqStream<PlaceDescription> stream(String regionFullName, String placeName, String placeTypeValue, User user) {
        JinqStream<PlaceDescription> _query = stream(PlaceDescription.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(placeName)) {
            _query = _query.where(i -> i.getPlaceName() != null && i.getPlaceName().contains(placeName));
        }

        if (StringUtils.hasLength(placeTypeValue)) {
            _query = _query.where(i -> i.getPlaceTypeValue() != null && i.getPlaceTypeValue().equals(placeTypeValue));
        }

        return _query;
    }

    @Override
    public String getProjectType() {
        return "重点场所";
    }

    @Override
    public List<PlaceDescription> fuzzyProjectDescription(String type, User user) {
        String _typeValue = dictionaryRepository.getVal(TYPE_DICT_NAME, type, true);
        return StringUtils.hasLength(_typeValue) ? stream(null, null, _typeValue, user)
                .collect(Collectors.toList()) : Collections.emptyList();
    }

    @Override
    public PlaceDescription fromForm(String subType, Form form) {
        PlaceDescription _desc;
        if (StringUtils.hasLength(form.getRefId())) {
            // 更新
            Result<PlaceDescription> _r = get(form.getRefId());
            if (_r.isOK()
                    // 专题记录未更新过
                    && (_r.data.getMaintainTime() == null
                    // 初始化的专项记录被更新后更新时间不应为null，避免异常
                    // 专项记录更新时间在专题记录更新时间之后
                    || (form.getMaintainTime() != null && _r.data.getMaintainTime().before(form.getMaintainTime())))) {
                _desc = _r.data;
                Optional.ofNullable(form.getSiteName()).ifPresent(_desc::setPlaceName);
                Optional.ofNullable(form.getPersonInCharge()).ifPresent(_desc::setPersonInCharge);
                Optional.ofNullable(form.getPersonInChargeCredentialNo()).ifPresent(_desc::setCredentialNo);
                Optional.ofNullable(form.getPersonInChargeContact()).ifPresent(_desc::setContact);
                Optional.ofNullable(form.getDetailedAddress()).ifPresent(_desc::setAddress);
                Optional.ofNullable(form.getPermanentPopulation()).ifPresent(_desc::setPermanentPopulation);
                Optional.ofNullable(form.getExternalPopulation()).ifPresent(_desc::setExternalPopulation);
                Optional.ofNullable(form.getArea()).ifPresent(_desc::setArea);
                Optional.ofNullable(form.getAttachmentIdsJSON()).ifPresent(_desc::setAttachmentIdsJSON);
            } else {
                // 不更新
                _desc = null;
            }
        } else {
            // 新增
            String _typeValue = dictionaryRepository.getVal(TYPE_DICT_NAME, subType, true);
            if (StringUtils.hasLength(_typeValue)) {
                _desc = new PlaceDescription();
                _desc.setPlaceTypeValue(_typeValue);
                _desc.setPlaceName(Optional.ofNullable(form.getSiteName()).orElse(""));
                _desc.setPersonInCharge(Optional.ofNullable(form.getPersonInCharge()).orElse(""));
                _desc.setCredentialNo(Optional.ofNullable(form.getPersonInChargeCredentialNo()).orElse(""));
                _desc.setContact(Optional.ofNullable(form.getPersonInChargeContact()).orElse(""));
                _desc.setAddress(Optional.ofNullable(form.getDetailedAddress()).orElse(""));
                _desc.setPermanentPopulation(form.getPermanentPopulation());
                _desc.setExternalPopulation(form.getExternalPopulation());
                _desc.setArea(form.getArea());
                _desc.setAttachmentIdsJSON(form.getAttachmentIdsJSON());
            } else {
                // 不更新
                _desc = null;
            }
        }
        return _desc;
    }

    public Result<List<Pair<String, Long>>> myStats(User user) {
        Result<List<Pair<String, Long>>> _result = new Result<>();
        String _userId = user.getId();

        List<Pair<String, Long>> _placeCountList = stream(PlaceDescription.class)
                .where(i -> i.getCreatorId().equals(_userId))
                .group(i -> i.getPlaceTypeValue(), (type, stream) -> stream.count())
                .toList();
        List<Dictionary> _placeTypeDicList = dictionaryRepository.fuzzy(TYPE_DICT_NAME, null,true, null);

        List<Pair<String, Long>> _countList = _placeTypeDicList.stream()
                        .map(placeTypeDic -> _placeCountList.stream().filter(p -> p.getOne().equals(placeTypeDic.getVal()))
                                .findFirst().map(p -> new Pair<>(placeTypeDic.getName(), p.getTwo()))
                                .orElse(new Pair<>(placeTypeDic.getName(), 0L)))
                .collect(Collectors.toList());

        _result.data = _countList;
        return _result;
    }

    /**
     * 检查数据权限
     *
     * @param regionId 行政区域id
     * @param user 用户信息
     * @return 是否合法的数据权限
     */
    private boolean validDataScope(String regionId, User user) {
        return departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .anyMatch(i -> i.getId().equals(regionId));
    }

    private void convertDictionaryFields(PlaceDescription placeDescription) {
        if (Objects.isNull(placeDescription)) {
            return;
        }

        if (StringUtils.hasLength(placeDescription.getPlaceTypeValue())) {
            List<Dictionary> _placeTypeDicList = dictionaryRepository.fuzzy(TYPE_DICT_NAME, null,true, null);
            placeDescription.setPlaceTypeName(_placeTypeDicList.stream()
                    .filter(d -> d.getVal().equals(placeDescription.getPlaceTypeValue()))
                    .map(Dictionary::getName)
                    .findFirst()
                    .orElse(placeDescription.getPlaceTypeValue()));
        }

        placePersonRepository.convertDictionaryFields(placeDescription.getPersons());
    }

    /**
     * 获取列值
     *
     * @param headerIndexMap 列索引映射
     * @param colValues 列值数组
     * @param colName 列名
     * @param required 是否必填
     * @return 列值
     * @throws Exception 异常
     */
    private String getColValue(Map<String, Integer> headerIndexMap, String[] colValues, String colName, boolean required) throws IllegalArgumentException {
        Integer _headerIndex = Optional.ofNullable(headerIndexMap.get(colName))
                .orElseThrow(() -> new IllegalArgumentException(String.format("未找到列%s", colName)));
        if (required && !StringUtils.hasLength(colValues[_headerIndex])) {
            throw new IllegalArgumentException(String.format("%s为必填项", colName));
        }
        return colValues[_headerIndex];
    }

    private Double getColDoubleValue(Map<String, Integer> headerIndexMap, String[] colValues, String colName, boolean required) throws IllegalArgumentException {
        String _colValue = getColValue(headerIndexMap, colValues, colName, required);
        Double _doubleValue = 0d;
        try {
            if (StringUtils.hasLength(_colValue)) {
                _doubleValue = Double.valueOf(_colValue);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(String.format("%s，无效的数字%s", colName, _colValue));
        }
        return _doubleValue;
    }

    void parseAttachments(List<PlaceDescription> items, boolean brief) {
        for (PlaceDescription i : items) {
            i.setAttachmentIds(StringUtils.hasLength(i.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(i.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : null);
        }

        if (!brief) {
            List<String> _ids = items.stream()
                    .filter(i -> i.getAttachmentIds() != null)
                    .flatMap(i -> i.getAttachmentIds().stream())
                    .collect(Collectors.toList());

            List<Media> _medias = mediaRepository.query(_ids, null);

            for (PlaceDescription i : items) {
                i.setAttachments(CollectionUtils.isEmpty(i.getAttachmentIds()) ? null : _medias.stream()
                        .filter(j -> i.getAttachmentIds().contains(j.getId()))
                        .collect(Collectors.toList()));
            }
        }
    }

    @Transactional(readOnly = true)
    public Tuple4<Long, Long, Long, List<String>> getInstabilityDifference(String type, User user) {
        List<Dictionary> placeTypeDic = dictionaryRepository.fuzzy(TYPE_DICT_NAME, null, true, null);

        // 获取所有或指定类别
        List<Dictionary> typeDicList = placeTypeDic.stream().filter(d -> !StringUtils.hasLength(type) || d.getVal().equals(type))
                .collect(Collectors.toList());
        List<String> typeList = typeDicList.stream().map(Dictionary::getName).collect(Collectors.toList());

        String typeMappingJson = dictionaryRepository.getVal("重点场所", "村社区管理类型映射", true);
        JsonObject typeMapping = ConverterUtil.json2Object(typeMappingJson, JsonObject.class);

        typeList = typeList.stream()
                .filter(t -> typeMapping.keySet().contains(t))
                .collect(Collectors.toList());

        // 构造SQL字段
        String queryFields = typeList.stream()
                .map(typeMapping::get)
                .filter(Objects::nonNull)
                .map(JsonElement::getAsString)
                .map(n -> "`村社区管理-" + n + "`, `专题管理-" + n + "`")
                .collect(Collectors.joining(", "));

        // 从视图查询数据
        List<Object[]> resultList = getCurrentSession().createNativeQuery(
                        "SELECT " + queryFields + " FROM subject_instability_difference WHERE `村/社区` LIKE :regionFullName")
                .setParameter("regionFullName", ("默认".equals(user.getDeptFullName()) ? "" : user.getDeptFullName()) + "%")
                .getResultList();

        long villageCount = 0L;
        long subjectCount = 0L;
        List<String> details = new ArrayList<>();
        for (int i = 0; i < typeList.size(); i++) {
            long v = 0L, s = 0L;
            for (Object[] o : resultList) {
                v += Long.parseLong(o[i * 2].toString());
                s += Long.parseLong(o[i * 2 + 1].toString());
            }
            villageCount += v;
            subjectCount += s;
            long diff = v - s;
            String typeName = typeList.get(i);
            details.add("上报" + typeName + v + "个，已建档" + s + "个，差异" + diff + "个");
        }
        return new Tuple4<>(villageCount, subjectCount, villageCount - subjectCount, details);
    }
}
