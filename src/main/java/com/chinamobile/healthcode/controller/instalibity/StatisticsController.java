package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.repository.instability.StatisticsRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Tuple5;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Optional;

@Controller
@RequestMapping("instability/statistics")
public class StatisticsController {
    private final StatisticsRepository statisticsRepository;
    private final LoginUtil loginUtil;

    public StatisticsController(StatisticsRepository statisticsRepository,
                                LoginUtil loginUtil) {
        this.statisticsRepository = statisticsRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping
    @RequiresPermissions("instability:statistics:index")
    public String index() {
        return "instability/statistics";
    }

    @PostMapping("/fuzzy")
    @ResponseBody
    @RequiresPermissions("instability:statistics:fuzzy")
    public Result<PagingItems<Tuple5<String, String, Long, Long, Long>>> fuzzy(@RequestBody JsonObject data) {
        int count = Optional.ofNullable(data.get("count"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(-1);
        int index = Optional.ofNullable(data.get("index"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(-1);
        String regionId = Optional.ofNullable(data.get("regionId"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        return statisticsRepository.fuzzy(count, index, regionId, loginUtil.getUser());
    }

    @PostMapping(value = "/export/base64")
    @ResponseBody
    @RequiresPermissions(value = "instability:statistics:export")
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String regionId = Optional.ofNullable(data.get("regionId"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        return statisticsRepository.export(regionId, loginUtil.getUser());
    }
}
