package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.EventRegionalCrimeInfo;
import com.chinamobile.healthcode.repository.instability.EventRegionalCrimeInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/event-regional-crime-info")
public class EventRegionalCrimeInfoController {
    private final LoginUtil loginUtil;
    private final EventRegionalCrimeInfoRepository eventInfoRepository;

    public EventRegionalCrimeInfoController(LoginUtil loginUtil, EventRegionalCrimeInfoRepository eventInfoRepository) {
        this.loginUtil = loginUtil;
        this.eventInfoRepository = eventInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:event-regional-crime-info:index")
    public String index() {
        return "/instability/event-regional-crime-info-table";
    }

    @GetMapping(value = "/form")
    @RequiresPermissions(value = "instability:event-regional-crime-info:save")
    public String form() {
        return "/instability/event-regional-crime-info-form";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:event-regional-crime-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<EventRegionalCrimeInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<EventRegionalCrimeInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = eventInfoRepository.fuzzy(count, index, regionFullName, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "instability:event-regional-crime-info:get")
    public Result<EventRegionalCrimeInfo> get(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();

        return eventInfoRepository.get(id);
    }

    @PostMapping(value = "/categories")
    @ResponseBody
    public Result<JsonObject> categories() {
        return eventInfoRepository.getCategories();
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:event-regional-crime-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody EventRegionalCrimeInfo item) {
        return eventInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:event-regional-crime-info:remove")
    @ResponseBody
    public Result<EventRegionalCrimeInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return eventInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:event-regional-crime-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return eventInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
