package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.repository.instability.ComparisonRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Tuple8;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Optional;

@Controller
@RequestMapping("instability/comparison")
public class ComparisonController {
    private final ComparisonRepository comparisonRepository;
    private final LoginUtil loginUtil;

    public ComparisonController(ComparisonRepository comparisonRepository,
                                LoginUtil loginUtil) {
        this.comparisonRepository = comparisonRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping
    @RequiresPermissions("instability:comparison:index")
    public String index() {
        return "instability/comparison";
    }

    @PostMapping("/fuzzy")
    @ResponseBody
    @RequiresPermissions("instability:comparison:fuzzy")
    public Result<PagingItems<Tuple8<String, String, Long, Long, Long, Long, Long, Long>>> fuzzy(@RequestBody JsonObject data) {
        int count = Optional.ofNullable(data.get("count"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(-1);
        int index = Optional.ofNullable(data.get("index"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(-1);
        String regionId = Optional.ofNullable(data.get("regionId"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        return comparisonRepository.fuzzy(count, index, regionId, loginUtil.getUser());
    }

    @PostMapping(value = "/export/base64")
    @ResponseBody
    @RequiresPermissions(value = "instability:comparison:export")
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String regionId = Optional.ofNullable(data.get("regionId"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        return comparisonRepository.export(regionId, loginUtil.getUser());
    }
}
