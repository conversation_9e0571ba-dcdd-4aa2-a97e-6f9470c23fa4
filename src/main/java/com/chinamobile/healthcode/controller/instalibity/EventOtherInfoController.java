package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.EventOtherInfo;
import com.chinamobile.healthcode.repository.instability.EventOtherInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/event-other-info")
public class EventOtherInfoController {
    private final LoginUtil loginUtil;
    private final EventOtherInfoRepository eventInfoRepository;

    public EventOtherInfoController(LoginUtil loginUtil, EventOtherInfoRepository eventInfoRepository) {
        this.loginUtil = loginUtil;
        this.eventInfoRepository = eventInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:event-other-info:index")
    public String index() {
        return "/instability/event-other-info-table";
    }

    @GetMapping(value = "/form")
    @RequiresPermissions(value = "instability:event-other-info:save")
    public String form() {
        return "/instability/event-other-info-form";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:event-other-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<EventOtherInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<EventOtherInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = eventInfoRepository.fuzzy(count, index, regionFullName, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "instability:event-other-info:get")
    public Result<EventOtherInfo> get(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();

        return eventInfoRepository.get(id);
    }

    @PostMapping(value = "/categories")
    @ResponseBody
    public Result<JsonObject> categories() {
        return eventInfoRepository.getCategories();
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:event-other-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody EventOtherInfo item) {
        return eventInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:event-other-info:remove")
    @ResponseBody
    public Result<EventOtherInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return eventInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:event-other-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return eventInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
