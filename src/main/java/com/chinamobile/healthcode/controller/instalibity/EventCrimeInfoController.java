package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.EventCrimeInfo;
import com.chinamobile.healthcode.repository.instability.EventCrimeInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/event-crime-info")
public class EventCrimeInfoController {
    private final LoginUtil loginUtil;
    private final EventCrimeInfoRepository eventInfoRepository;

    public EventCrimeInfoController(LoginUtil loginUtil, EventCrimeInfoRepository eventInfoRepository) {
        this.loginUtil = loginUtil;
        this.eventInfoRepository = eventInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:event-crime-info:index")
    public String index() {
        return "/instability/event-crime-info-table";
    }

    @GetMapping(value = "/form")
    @RequiresPermissions(value = "instability:event-crime-info:save")
    public String form() {
        return "/instability/event-crime-info-form";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:event-crime-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<EventCrimeInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<EventCrimeInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = eventInfoRepository.fuzzy(count, index, regionFullName, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "instability:event-crime-info:get")
    public Result<EventCrimeInfo> get(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();

        return eventInfoRepository.get(id);
    }

    @PostMapping(value = "/categories")
    @ResponseBody
    public Result<JsonObject> categories() {
        return eventInfoRepository.getCategories();
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:event-crime-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody EventCrimeInfo item) {
        return eventInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:event-crime-info:remove")
    @ResponseBody
    public Result<EventCrimeInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return eventInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:event-crime-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return eventInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
