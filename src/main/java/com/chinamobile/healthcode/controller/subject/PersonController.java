package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.healthcode.repository.subject.PersonRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "subject/person")
public class PersonController {

    final PersonRepository personRepository;
    final DefaultRoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;
    final LoginUtil loginUtil;

    public PersonController(PersonRepository personRepository, DefaultRoleRepository roleRepository, DictionaryRepository dictionaryRepository, LoginUtil loginUtil) {
        this.personRepository = personRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "subject:person:index")
    public String persons() {
        return "subject/persons";
    }

    @GetMapping(value = "/form")
    @RequiresPermissions(value = "subject:person:index")
    public String person() {
        return "subject/person";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "subject:person:get")
    public Result<PersonDescription> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<PersonDescription> _item = personRepository.get(_id);
        if (_item.isOK() && !roleRepository.isUserInRoleCached(loginUtil.getUserId(), PersonRepository.ADMIN_ROLE)) {
            if (_item.data.getCredentialNo() != null && _item.data.getCredentialNo().length() > 4) {
                int _length = _item.data.getCredentialNo().length();
                String _credentialNo = _item.data.getCredentialNo().substring(0, 2);
                _credentialNo += StringUtils.repeat("*", _length - 4);
                _credentialNo += _item.data.getCredentialNo().substring(_length - 2);

                _item.data.setCredentialNo(_credentialNo);
            }
        }

        return _item;
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "subject:person:fuzzy")
    public Result<PagingItems<PersonDescription>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        PersonDescription.ENUM_TYPE _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : PersonDescription.ENUM_TYPE.valueOf(i.getAsString())).orElse(null);
        PersonDescription.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : PersonDescription.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        boolean _origin = Optional.ofNullable(data.get("origin"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsBoolean)
                .orElse(false);

        Result<PagingItems<PersonDescription>> _page = new Result<>();
        _page.data = personRepository.fuzzy(_count, _index, _sortFields, _regionFullName, _name, _type, _status, loginUtil.getUser());

        // 脱敏
        if (!_origin && !roleRepository.isUserInRoleCached(loginUtil.getUserId(), PersonRepository.ADMIN_ROLE)) {
            for (PersonDescription i : _page.data.items) {
                if (i.getCredentialNo() != null && i.getCredentialNo().length() > 4) {
                    int _length = i.getCredentialNo().length();
                    String _credentialNo = i.getCredentialNo().substring(0, 2);
                    _credentialNo += StringUtils.repeat("*", _length - 4);
                    _credentialNo += i.getCredentialNo().substring(_length - 2);

                    i.setCredentialNo(_credentialNo);
                }
            }
        }

        return _page;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "subject:person:save")
    public Result<String> save(@RequestBody PersonDescription item) {
        return personRepository.save(item, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "subject:person:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return personRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @ResponseBody
    @RequiresPermissions(value = "subject:person:import")
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        File _file = null;

        try {
            _file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(file.getInputStream(), _file);

            return personRepository.importFromExcel(_file, loginUtil.getUser());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @PostMapping(value = "/types")
    @ResponseBody
    public Result<List<Dictionary>> types() {
        Result<List<Dictionary>> _vals = new Result<>();
        _vals.data = Arrays.stream(PersonDescription.ENUM_TYPE.values())
                .map(t -> {
                    Dictionary _d = new Dictionary();
                    _d.setName(t.name());
                    _d.setVal(t.name());
                    return _d;
                })
                .collect(Collectors.toList());
        return _vals;
    }

    @PostMapping("/options")
    @ResponseBody
    public Result<JsonObject> options() {
        Result<JsonObject> _optionsResult = new Result<>();
        _optionsResult.data = personRepository.queryOptions();
        return _optionsResult;
    }

    @PostMapping("/stats/my")
    @ResponseBody
    public Result<List<Pair<String, Long>>> myStats() {
        return personRepository.myStats(loginUtil.getUser());
    }

    @PostMapping("/sync/profile")
    @ResponseBody
    @RequiresPermissions(value = "subject:person:sync")
    public Result<Void> synchronizeToProfile() {
        Result<Void> _syncResult = new Result<>();
        personRepository.synchronizeToProfile();
        return _syncResult;
    }

    @PostMapping("/instability/difference")
    @ResponseBody
    @RequiresPermissions(value = "subject:person:fuzzy")
    public Result<Tuple4<Long, Long, Long, List<String>>> getStatsDifference(@RequestBody JsonObject data) {
        Result<Tuple4<Long, Long, Long, List<String>>> res = new Result<>();
        String type = Optional.ofNullable(data.get("type"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        res.data = personRepository.getInstabilityDifferenceWithDetails(type, loginUtil.getUser());
        return res;
    }
}
