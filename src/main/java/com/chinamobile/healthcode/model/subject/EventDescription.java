package com.chinamobile.healthcode.model.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Entity
@Table(name = "event_descriptions")
public class EventDescription extends AbstractEntity implements ProjectDescription {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 128, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 128)
    String title;

    @Column(length = 36)
    String regionId;

    @Column(length = 128)
    String regionFullName;

    @Column(length = 36, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 36)
    String type;

    @Column(length = 36, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 36)
    String subtype;

    @Column(columnDefinition = "text", nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    String description;

    @Column(columnDefinition = "text", nullable = false)
    @NotNull(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    String address;

    // gcj经度
    Double lng;

    // gcj纬度
    Double lat;

    // wgs经度
    Double lngInWgs;

    // wgs纬度
    Double latInWgs;

    @Column(nullable = false)
    @NotNull(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    Date beginTime;

    boolean finished;

    @Override
    public Form toForm() {
        Form _form = new Form();
        _form.setRefId(getId());

        _form.setEventName(getTitle());
        _form.setEventAddress(getAddress());
        _form.setEventDescription(getDescription());

        _form.setRegionId(getRegionId());
        _form.setRegionFullName(getRegionFullName());
        _form.setLng(getLng());
        _form.setLat(getLat());
        _form.setLngInWgs(getLngInWgs());
        _form.setLatInWgs(getLatInWgs());

        return _form;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = StringUtils.trimWhitespace(title);
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubtype() {
        return subtype;
    }

    public void setSubtype(String subtype) {
        this.subtype = subtype;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = StringUtils.trimWhitespace(description);
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = StringUtils.trimWhitespace(address);
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public boolean getFinished() {
        return finished;
    }

    public void setFinished(boolean finished) {
        this.finished = finished;
    }

}
