package com.chinamobile.healthcode.model.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import javax.persistence.Enumerated;
import javax.persistence.EnumType;

@Entity
@Table(name = "subject_persons")
public class PersonDescription extends AbstractEntity implements ProjectDescription {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    /**
     * 姓名
     */
    @Column(length = 32, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 32)
    String name;

    /**
     * 证件类型
     * 身份证
     * 港澳居民来往内地通行证
     * 护照
     * 其它
     */
    @Column(length = 32, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 32)
    String credentialType;

    /**
     * 证件号码
     */
    @Column(length = 64, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 64)
    String credentialNo;

    /**
     * 联系方式
     */
    @Column(length = 16)
    String contact;

    /**
     * 归属网格id
     */
    @Column(length = 36)
    String regionId;

    /**
     * 归属网格
     */
    @Column(length = 128)
    String regionFullName;

    /**
     * 现居住地
     * 重点未成年人-监护人居住详址
     */
    @Column(columnDefinition = "text", nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class}, message = "现居住地不能为空")
    String address;

    /**
     * gcj经度
     */
    Double lng;

    /**
     * gcj纬度
     */
    Double lat;

    /**
     * wgs经度
     */
    Double lngInWgs;

    /**
     * wgs纬度
     */
    Double latInWgs;

    /**
     * 性别
     */
    boolean isMale = true;

    /**
     * 人员类别
     */
    @Column(nullable = false)
    @NotNull
    @Enumerated(EnumType.STRING)
    ENUM_TYPE type;

    /**
     * 人员子类型
     */
    @Column(length = 32)
    @Size(max = 32)
    String subtype;

    // 最后一次尿检时间
    Date lastUrineTime;

    // 矫正开始时间
    Date rectifyFrom;

    // 矫正期限
    @Column(length = 32)
    @Size(max = 32)
    String rectifyTimeLimit;

    // 上访事由
    @Column(columnDefinition = "text")
    String petition;

    // 是否已签订协议
    Boolean archiveAgreement;

    // 监护人姓名
    @Column(length = 32)
    @Size(max = 32)
    String guardianName;

    // 监护人关系
    @Column(length = 32)
    @Size(max = 32)
    String guardianRelationship;

    // 监护人联系方式
    @Column(length = 16)
    @Size(max = 16)
    String guardianContact;

    // 是否低保户
    Boolean isLowIncomeFamily;

    /**
     * 人员状态
     */
    @Column(nullable = false)
    @NotNull
    ENUM_STATUS status;

    @Column(length = 8)
    String riskLevel;

    /**
     * 人员类型
     */
    @Column(length = 16, nullable = false)
    @NotBlank(groups= { ValidationGroup.SubjectPersonMinors.class }, message = "人员类型不能为空")
    String populationType;

    /**
     * 备注
     */
    @Column(columnDefinition = "text")
    String memo;

    // 重点未成年人
    /**
     * 人员等级
     */
    @Column(length = 16)
    @NotBlank(groups= { ValidationGroup.SubjectPersonMinors.class }, message = "人员等级不能为空")
    String levelOfConcern;

    /**
     * 人员分类
     */
    @Column(length = 255)
    @NotBlank(groups= { ValidationGroup.SubjectPersonMinors.class }, message = "人员分类不能为空")
    String categoryOfMinor;

    /**
     * 学校
     */
    @Column(length = 255)
    @Size(max = 255)
    String schoolName;

    /**
     * 家庭情况
     */
    @Column(length = 255)
    @NotBlank(groups= { ValidationGroup.SubjectPersonMinors.class }, message = "家庭情况不能为空")
    String familyStatus;

    /**
     * 监护人身份号码
     */
    @Column(length = 64)
    @Size(max = 64)
    String guardianCredentialNo;

    /**
     * 帮扶手段
     */
    @Column(length = 255)
    String wayToHelp;

    /**
     * 帮扶人姓名
     */
    @Column(length = 32)
    @Size(max = 32)
    String helperName;

    /**
     * 帮扶人联系方式
     */
    @Column(length = 16)
    @Size(max = 16)
    String helperContact;

    /**
     * 帮扶情况
     */
    @Column(length = 255)
    @Size(max = 255)
    String helpSituation;

    /**
     * 主责部门
     */
    @Column(length = 255)
    @Size(max = 255)
    String responsibleDepartment;

    /**
     * 主责人
     */
    @Column(length = 32)
    @Size(max = 32)
    String responsiblePersonName;

    /**
     * 主责人联系电话
     */
    @Column(length = 16)
    @Size(max = 16)
    String responsiblePersonContact;

    @Override
    public Form toForm() {
        Form _form = new Form();
        _form.setRefId(getId());

        _form.setPersonName(getName());
        _form.setSex(getIsMale());
        _form.setCredentialType(getCredentialType());
        _form.setCredentialNo(getCredentialNo());
        _form.setResidentialAddress(getAddress());
        _form.setMajorPersonState(getStatus().name());

        // 严重精神障碍患者
        _form.setLowIncomeFamily(getIsLowIncomeFamily());
        _form.setGuardianName(getGuardianName());
        _form.setGuardianRelationship(getGuardianRelationship());
        _form.setGuardianContact(getGuardianContact());
        _form.setRiskLevel(getRiskLevel());
        // 吸毒人员
        _form.setLastUrineTime(getLastUrineTime());
        // 上访人员
        _form.setPetition(getPetition());
        _form.setAchieveAgreement(getArchiveAgreement());
        // 社区矫正人员
        _form.setRectifyFrom(getRectifyFrom());
        _form.setRectifyTimeLimit(getRectifyTimeLimit());
        _form.setContact(getContact());

        _form.setRegionId(getRegionId());
        _form.setRegionFullName(getRegionFullName());
        _form.setLng(getLng());
        _form.setLat(getLat());
        _form.setLngInWgs(getLngInWgs());
        _form.setLatInWgs(getLatInWgs());

        return _form;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(String credentialType) {
        this.credentialType = StringUtils.trimWhitespace(credentialType);
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = StringUtils.trimWhitespace(credentialNo);
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = StringUtils.trimWhitespace(contact);
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = StringUtils.trimWhitespace(address);
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public boolean getIsMale() {
        return isMale;
    }

    public void setIsMale(boolean male) {
        isMale = male;
    }

    public ENUM_TYPE getType() {
        return type;
    }

    public void setType(ENUM_TYPE type) {
        this.type = type;
    }

    public String getSubtype() {
        return subtype;
    }

    public void setSubtype(String subtype) {
        this.subtype = StringUtils.trimWhitespace(subtype);
    }

    public Date getLastUrineTime() {
        return lastUrineTime;
    }

    public void setLastUrineTime(Date lastUrineTime) {
        this.lastUrineTime = lastUrineTime;
    }

    public Date getRectifyFrom() {
        return rectifyFrom;
    }

    public void setRectifyFrom(Date rectifyFrom) {
        this.rectifyFrom = rectifyFrom;
    }

    public String getRectifyTimeLimit() {
        return rectifyTimeLimit;
    }

    public void setRectifyTimeLimit(String rectifyTimeLimit) {
        this.rectifyTimeLimit = StringUtils.trimWhitespace(rectifyTimeLimit);
    }

    public String getPetition() {
        return petition;
    }

    public void setPetition(String petition) {
        this.petition = StringUtils.trimWhitespace(petition);
    }

    public Boolean getArchiveAgreement() {
        return archiveAgreement;
    }

    public void setArchiveAgreement(Boolean archiveAgreement) {
        this.archiveAgreement = archiveAgreement;
    }

    public String getGuardianName() {
        return guardianName;
    }

    public void setGuardianName(String guardianName) {
        this.guardianName = StringUtils.trimWhitespace(guardianName);
    }

    public String getGuardianRelationship() {
        return guardianRelationship;
    }

    public void setGuardianRelationship(String guardianRelationship) {
        this.guardianRelationship = StringUtils.trimWhitespace(guardianRelationship);
    }

    public String getGuardianContact() {
        return guardianContact;
    }

    public void setGuardianContact(String guardianContact) {
        this.guardianContact = StringUtils.trimWhitespace(guardianContact);
    }

    public Boolean getIsLowIncomeFamily() {
        return isLowIncomeFamily;
    }

    public void setIsLowIncomeFamily(Boolean lowIncomeFamily) {
        isLowIncomeFamily = lowIncomeFamily;
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getPopulationType() {
        return populationType;
    }

    public void setPopulationType(String populationType) {
        this.populationType = populationType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getLevelOfConcern() {
        return levelOfConcern;
    }

    public void setLevelOfConcern(String levelOfConcern) {
        this.levelOfConcern = levelOfConcern;
    }

    public String getCategoryOfMinor() {
        return categoryOfMinor;
    }

    public void setCategoryOfMinor(String categoryOfMinor) {
        this.categoryOfMinor = categoryOfMinor;
    }

    public String getSchoolName() {
        return schoolName;
    }

    public void setSchoolName(String schoolName) {
        this.schoolName = schoolName;
    }

    public String getFamilyStatus() {
        return familyStatus;
    }

    public void setFamilyStatus(String familyStatus) {
        this.familyStatus = familyStatus;
    }

    public String getGuardianCredentialNo() {
        return guardianCredentialNo;
    }

    public void setGuardianCredentialNo(String guardianCredentialNo) {
        this.guardianCredentialNo = guardianCredentialNo;
    }

    public String getWayToHelp() {
        return wayToHelp;
    }

    public void setWayToHelp(String wayToHelp) {
        this.wayToHelp = wayToHelp;
    }

    public String getHelperName() {
        return helperName;
    }

    public void setHelperName(String helperName) {
        this.helperName = helperName;
    }

    public String getHelperContact() {
        return helperContact;
    }

    public void setHelperContact(String helperContact) {
        this.helperContact = helperContact;
    }

    public String getHelpSituation() {
        return helpSituation;
    }

    public void setHelpSituation(String helpSituation) {
        this.helpSituation = helpSituation;
    }

    public String getResponsibleDepartment() {
        return responsibleDepartment;
    }

    public void setResponsibleDepartment(String responsibleDepartment) {
        this.responsibleDepartment = responsibleDepartment;
    }

    public String getResponsiblePersonName() {
        return responsiblePersonName;
    }

    public void setResponsiblePersonName(String responsiblePersonName) {
        this.responsiblePersonName = responsiblePersonName;
    }

    public String getResponsiblePersonContact() {
        return responsiblePersonContact;
    }

    public void setResponsiblePersonContact(String responsiblePersonContact) {
        this.responsiblePersonContact = responsiblePersonContact;
    }

    public enum ENUM_TYPE {
        严重精神障碍患者("严重精神障碍患者"), 
        吸毒人员("吸毒人员"), 
        上访人员("上访人员"), 
        刑满释放人员("刑满释放人员"),
        刑满释放安置帮教人员("刑满释放安置帮教人员"),
        社区矫正人员("社区矫正人员"), 
        重点未成年人("重点未成年人"),
        政治安全重点人员("政治安全重点人员"),
        律师重点人员("律师重点人员"),
        邪教人员("邪教人员"),
        网络重点人员("网络重点人员"),
        涉众金融投资受损人员("涉众金融投资受损人员"),
        军队退役人员("军队退役人员"),
        疫苗受害人员("疫苗“受害”人员"),
        三新从业人员("“三新”从业人员"),
        三失一偏人员("“三失一偏”(生活失意、心态失衡、行为失常人员和性格偏执)人员"),
        低保在册人员("低保在册人员");

        final String displayName;

        ENUM_TYPE(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }


    public enum ENUM_STATUS {
        在管(0), 失管(1), 已故(2), 期满(3), 注销(4);

        int value;

        ENUM_STATUS(int value) {
            this.value = value;
        }

        public static ENUM_STATUS valueOf(int value) {
            if (value == 0) {
                return 在管;
            } else if (value == 1) {
                return 失管;
            } else if (value == 2) {
                return 已故;
            } else if (value == 3) {
                return 期满;
            } else if (value == 4) {
                return 注销;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

}
