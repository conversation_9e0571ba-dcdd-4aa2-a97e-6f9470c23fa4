let searchbar, table;

function fuzzy(count, index, regionId) {
    const data = {
        count,
        index,
        regionId
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('statistics/fuzzy', data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function exportReport(regionId, regionFullName) {
    console.log(regionFullName.replaceAll('/', ''));
    new HttpRequest().ajax('statistics/export/base64', { regionId }, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            regionFullName = regionFullName.replaceAll('/', '');
            FileUtil.downloadFromBase64(`${regionFullName}不稳定因素排查化解表.xlsx`, null, res.data);
        }
    });
}

function initTable() {
    table = $('#table').table({
        'columns': [{
            'title': '行政区域',
            'field': 'two',
            'control': 'label'
        }, {
            'title': '重点人员数',
            'field': 'three',
            'control': 'label'
        }, {
            'title': '重点场所数',
            'field': 'four',
            'control': 'label'
        }, {
            'title': '重点事件数',
            'field': 'five',
            'control': 'label'
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].id : null);
        },
        'btns': [{
            'name': '导出',
            'icon': 'mdi-file-document-arrow-right-outline',
            'callback': item => {
                exportReport(item.one, item.two);
            }
        }]
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }]
    });

    initTable();

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                table.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            table.query();
        }
    });
}

$(function () {
    init();
});
