let searchbar, table;

function fuzzy(count, index, regionFullName) {
    const _data = {
        'count': count,
        'index': index,
        'regionFullName': regionFullName
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('event-online-opinion-info/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.region = [{
                        'id': val.regionId,
                        'fullName': val.regionFullName
                    }];
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('event-online-opinion-info/save', item).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('event-online-opinion-info/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('event-online-opinion-info/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('村（社）专项管理重点事件信息.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function initTable() {
    table = $('#table').ediTable({
        'columns': [{
            'title': '行政区域',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                },
                'rule': {
                    'required': true
                }
            },
            'width': 300
        }, {
            'title': '事件标题',
            'field': 'title',
            'control': 'text',
            'validator': {
                'rule': {
                    'required': true
                }
            },
            'width': 200
        }, {
            'title': '事件分类',
            'field': 'category',
            'control': 'text',
            'width': 120
        }, {
            'title': '事件类型',
            'field': 'type',
            'control': 'text',
            'width': 120
        }, {
            'title': '事件概述',
            'field': 'overview',
            'control': 'textarea',
            'width': 300
        }, {
            'title': '事发地点',
            'field': 'location',
            'control': 'text',
            'width': 180
        // }, {
        //     'title': '国测局经度',
        //     'field': 'lngInGcj',
        //     'control': 'text',
        //     'width': 120
        // }, {
        //     'title': '国测局纬度',
        //     'field': 'latInGcj',
        //     'control': 'text',
        //     'width': 120
        // }, {
        //     'title': 'WGS经度',
        //     'field': 'lngInWgs',
        //     'control': 'text',
        //     'width': 120
        // }, {
        //     'title': 'WGS纬度',
        //     'field': 'latInWgs',
        //     'control': 'text',
        //     'width': 120
        }, {
            'title': '事发时间',
            'field': 'incidentTime',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH:mm:ss'
            }
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null);
        },
        'create': true,
        'save': item => {
            return new Promise(resolve => {
                const _region = item.region && item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                
                // 删除region字段，避免传递给后端
                delete item.region;

                save(item).then(success => {
                    resolve({
                        'success': success,
                        'scrollToFirstPage': !item.hasOwnProperty('id')
                    });
                });
            });
        },
        'remove': remove,
        'scrollX': true,
        'scrollY': 'calc(100vh - 200px)'
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            }
        }]
    });

    initTable();

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                table.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            table.query();
        }
    });
}

$(function () {
    init();
});
