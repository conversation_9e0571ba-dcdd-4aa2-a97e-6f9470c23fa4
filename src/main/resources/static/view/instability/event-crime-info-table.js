let searchbar, table;

function fuzzy(count, index, regionFullName) {
    const _data = {
        'count': count,
        'index': index,
        'regionFullName': regionFullName
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('event-crime-info/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.region = [{
                        'id': val.regionId,
                        'fullName': val.regionFullName
                    }];
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function types() {
    return new Promise(resolve => {
        new HttpRequest().ajax('event-crime-info/types', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('event-crime-info/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('event-crime-info/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('村（社）专项管理重点事件信息.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function render(id) {
    ModalUtil.open({
        'title': id === null ? '新增事件' : '编辑事件',
        'type': 2,
        'maxmin': true,
        'area': ['55%', '80%'],
        'content': 'event-crime-info/form' + (id === null ? '' : ('?id=' + id)),
        'end': function () {
            table.query(id === null ? null : table.getIndexCurrent());
        }
    });
}

function initTable() {
    table = $('#table').table({
        'columns': [{
            'title': '行政区域',
            'field': 'regionFullName',
            'control': 'label',
            'width': 300
        }, {
            'title': '事件标题',
            'field': 'title',
            'control': 'label',
            'width': 200
        }, {
            'title': '事件分类',
            'field': 'category',
            'control': 'label',
            'width': 120
        }, {
            'title': '事件类型',
            'field': 'type',
            'control': 'label',
            'width': 120
        }, {
            'title': '事件概述',
            'field': 'overview',
            'control': 'label',
            'width': 300
        }, {
            'title': '事发地点',
            'field': 'address',
            'control': 'label',
            'width': 180
        }, {
            'title': '事发时间',
            'field': 'incidentTime',
            'control': 'label'
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null);
        },
        'btns': [{
            'callback': item => {
                render(item.id);
            }
        }, {
            'callback': remove
        }],
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                render(null);
            }
        }],
        'scrollX': true,
        'scrollY': 'calc(100vh - 200px)'
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            }
        }]
    });

    initTable();

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                table.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            table.query();
        }
    });
}

$(function () {
    init();
});
