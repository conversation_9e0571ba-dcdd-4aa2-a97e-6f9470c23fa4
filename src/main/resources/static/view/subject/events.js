let searchbar, items;

function fuzzy(count, index, sortBy, regionFullName, type, subtype, title, description, finished, beginTime, endTime, address) {
    const _data = {
        count,
        index,
        sortBy,
        regionFullName,
        type,
        subtype,
        title,
        description,
        finished,
        address
    };

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('event/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('event/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function types() {
    return new Promise(resolve => {
        new HttpRequest().ajax('event/types', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('event/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('重点事件.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function render(id) {
    ModalUtil.open({
        'title': id === null ? '新增事件' : '编辑事件',
        'type': 2,
        'maxmin': true,
        'area': ['90%', '90%'],
        'content': 'event/form' + (id === null ? '' : ('?id=' + id)),
        'end': function () {
            items.query(id === null ? null : items.getIndexCurrent());
        }
    });
}

function initItems() {
    items = $('#table').table({
        'columns': [{
            'title': '事件标题',
            'field': 'title',
            'control': 'label',
            'sortable': true
        }, {
            'title': '归属网格',
            'field': 'regionFullName',
            'control': 'label'
        }, {
            'title': '事件分类',
            'field': 'type',
            'control': 'label',
            'sortable': true
        }, {
            'title': '事件类型',
            'field': 'subtype',
            'control': 'label'
        }, {
            'title': '事件概述',
            'field': 'description',
            'control': 'label'
        }, {
            'title': '事发地点',
            'field': 'address',
            'control': 'label'
        }, {
            'title': '事发时间',
            'field': 'beginTime',
            'control': 'label',
            'sortable': true
        }],
        'query': (count, index, sortBy) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, sortBy, _regions.length > 0 ? _regions[0].fullName : null, searchbar.type(), searchbar.subtype(), searchbar.title(), searchbar.description(), searchbar.finished(), searchbar.beginTime(), searchbar.endTime(), searchbar.address());
        },
        'btns': [{
            'callback': item => {
                render(item.id);
            }
        }, {
            'callback': remove
        }],
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                render(null);
            }
        }]
    });
}

function getStatsDifference(type) {
    return new Promise(resolve => {
        const data = { type };
        new HttpRequest().ajax('event/instability/difference', data, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : 0);
        });
    });
}

// 统计差异显示
let $instabilityDifferenceDiv = null;
function updateStabilityDifference(type) {
    getStatsDifference(type).then(data => {
        if ($instabilityDifferenceDiv) {
            $instabilityDifferenceDiv.remove();
            $instabilityDifferenceDiv = null;
        }

        if (data) {
            let typeText = type ? `（${type}）` : '';
            let alertClass = data.three > 0 ? 'am-alert-danger' : (data.three < 0 ? 'am-alert-warning' : 'am-alert-secondary');
            let showCollapse = (type == null && Array.isArray(data.four) && data.four.length > 0);
            let arrowHtml = showCollapse ? '<span style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%);"><i class="mdi mdi-chevron-down"></i></span>' : '';
            $instabilityDifferenceDiv = $(`
                <div class="am-alert ${alertClass}" data-am-alert style="margin-top: 10px; position: relative;${showCollapse ? ' cursor:pointer;' : ''}" id="instability-diff-alert">
                    <p style="margin-bottom:0; padding-right: ${showCollapse ? '30px' : '0'};"><i class="mdi-alert-circle"></i> 上报重点事件${typeText}${data.one}件，已建档${data.two}件，差异${data.three}件</p>
                    ${arrowHtml}
                    <div id="instabilityTypeCollapse" class="panel-collapse collapse" style="margin-top:10px; display:none;"></div>
                </div>
            `);
            $('#searchbar').after($instabilityDifferenceDiv);
            if (showCollapse) {
                let detailsHtml = `<ul style='margin-bottom:0; color:inherit; background:transparent;'>${data.four.map(txt => `<li>${txt}</li>`).join('')}</ul>`;
                let $collapse = $instabilityDifferenceDiv.find('#instabilityTypeCollapse');
                $collapse.html(detailsHtml);
                $instabilityDifferenceDiv.click(function (e) {
                    if ($(e.target).closest('#instabilityTypeCollapse').length === 0) {
                        if ($collapse.is(':visible')) {
                            $collapse.slideUp(200);
                            $instabilityDifferenceDiv.find('.mdi-chevron-down, .mdi-chevron-up').removeClass('mdi-chevron-up').addClass('mdi-chevron-down');
                        } else {
                            $collapse.slideDown(200);
                            $instabilityDifferenceDiv.find('.mdi-chevron-down, .mdi-chevron-up').removeClass('mdi-chevron-down').addClass('mdi-chevron-up');
                        }
                    }
                });
            }
        }
    });
}

// 获取类别名称显示文本（可选，若type为value时需转为name）
function getTypeText(typeValue) {
    return new Promise(resolve => {
        if (!eventTypes || !typeValue) {
            resolve(null);
            return;
        }

        eventTypes.then(types => {
            if (types && types.options) {
                const found = types.options.find(opt => opt.key === typeValue);
                resolve(found ? found.value : null);
            } else {
                resolve(null);
            }
        });
    });
}

function init() {
    const _promise = types();

    let eventTypes;
    
    searchbar = $('#searchbar').searchbar({
        'layout': {
            'rows': 2
        },
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions',
            'row': 1
        }, {
            'field': 'type',
            'title': '分类',
            'icon': 'mdi-menu',
            'control': 'select',
            'validator': new Promise(resolve => {
                _promise.then(types => {
                    const _params = {
                        'options': []
                    };

                    for (const prop in types) {
                        _params.options.push({
                            'key': prop,
                            'value': prop
                        });
                    }

                    resolve(_params);
                });
            }),
            'ref': 'type',
            'row': 1
        }, {
            'field': 'subtype',
            'title': '类型',
            'icon': 'mdi-filter-menu',
            'control': 'cascader',
            'validator': new Promise(resolve => {
                _promise.then(types => {
                    resolve({
                        'relativeFieldIndex': 1,
                        'mapper': types
                    });
                });
            }),
            'ref': 'subtype',
            'row': 1
        }, {
            'title': '标题',
            'icon': 'mdi-archive-outline',
            'ref': 'title',
            'row': 1
        }, {
            'title': '事件概述',
            'icon': 'mdi-checkbook',
            'ref': 'description',
            'row': 1
        }, {
            'title': '状态',
            'icon': 'mdi-state-machine',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': false,
                    'value': '处置中'
                }, {
                    'key': true,
                    'value': '已化解'
                }]
            },
            'ref': 'finished',
            'row': 2
        }, {
            'title': '开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'beginTime',
            'row': 2
        }, {
            'title': '结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'endTime',
            'row': 2
        }, {
            'title': '事发地点',
            'icon': 'mdi-book-marker',
            'ref': 'address',
            'row': 2
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            },
            'row': 1
        }]
    });

    initItems();

    eventTypes = new Promise(resolve => {
        _promise.then(resolve);
    });

    // 监听类型选择变化
    let lastType = null;
    setInterval(() => {
        const currentType = searchbar.type();
        if (currentType !== lastType) {
            lastType = currentType;
            updateStabilityDifference(currentType);
        }
    }, 500);

    // 页面初始化时显示全部类别的统计差异
    updateStabilityDifference(null);

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                items.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
