let searchbar, app, currentSubject = 'person';

function query(currentSubject, regionId) {
    const _data = {
        'subjectType': currentSubject,
        'regionId': regionId
    };

    return new Promise(resolve => {
        new HttpRequest().ajax(`/subject/survey/query`, _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            console.log(res.data);
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function exportTo(regionId, min, max) {
    const _data = {
        'regionId': regionId
    };

    if (min !== '') {
        _data.min = min;
    }

    if (max !== '') {
        _data.max = max;
    }

    new HttpRequest().ajax('survey/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('网格.xlsx', null, res.data);
        }
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-marker-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                let _regions = searchbar.regions();
                // if (_regions.length === 0) {
                //     ModalUtil.alert('请选择行政区划');
                //     return;
                // }

                app.query(_regions[0] ? _regions[0].id : null);
            },
            'ref': 'query'
        // }, {
        //     'title': '导出',
        //     'icon': 'mdi-download',
        //     'callback': () => {
        //         let _regions = searchbar.regions();
        //         if (_regions.length === 0) {
        //             ModalUtil.alert('请选择行政区划');
        //             return;
        //         }
        //
        //         exportTo(_regions[0].id, searchbar.min(), searchbar.max());
        //     }
        }]
    });

    app = new Vue({
        'el': '#app',
        'data': {
            'subjects': [{
                'type': 'person',
                'name': '重点人员',
                'cols': [
                    '区县',
                    '镇/街道',
                    '社区/村居',
                    '网格',
                    '严重精神障碍患者',
                    '吸毒人员',
                    '上访人员',
                    '刑满释放人员',
                    '社区矫正人员',
                    '重点未成年人',
                    '60岁以上老人',
                    '低保对象',
                    '婴儿',
                    '孕产妇',
                    '孤寡老人',
                    '残疾人',
                    '流浪救助人员',
                    '留守儿童'
                ],
                'active': true
            }, {
                'type': 'place',
                'name': '重点场所',
                'cols': [
                    '区县',
                    '镇/街道',
                    '社区/村居',
                    '网格',
                    '出租屋',
                    '公寓',
                    '工厂',
                    '酒店',
                    '民宿',
                    '宗教场所',
                    '影院',
                    '广场',
                    '公园',
                    '市场',
                    '车站',
                    '超市',
                    '休闲会所',
                    '其他',
                    '饮食店',
                    '学校',
                    '车行',
                    '药店',
                    '理发店',
                    '物流站',
                    '村委会',
                    '服装店',
                    '养老院',
                    '卫生站',
                    '商店',
                    '培训机构'
                ]
            }, {
                'type': 'event',
                'name': '重点事件',
                'cols': [
                    '区县',
                    '镇/街道',
                    '社区/村居',
                    '网格',
                    '轻生',
                    '网络舆情',
                    '社会热议',
                    '重大安全事故',
                    '灯会、游神赛会等大型群体性活动',
                    '火灾事故'
                ]
            }, {
                'type': 'property',
                'name': '重点设施',
                'cols': [
                    '区县',
                    '镇/街道',
                    '社区/村居',
                    '网格',
                    '消防栓',
                    '沟井盖',
                    '违章建筑',
                    '危险品仓储',
                    '余泥渣土',
                    '危险边坡',
                    '积水点'
                ]
            }, {
                'type': 'unit',
                'name': '重点单位',
                'cols': [
                    '区县',
                    '镇/街道',
                    '社区/村居',
                    '网格',
                    '党政机关',
                    '学校',
                    '企业'
                ]
            }],
            'items': {
                'person': [],
                'place': [],
                'event': [],
                'property': [],
                'unit': []
            }
        },
        'methods': {
            'query': function (regionId) {
                let that = this;

                query(currentSubject, regionId).then(items => {
                    that.items[currentSubject].splice(0, that.items[currentSubject].length);

                    if (items && items.length) {
                        let subject = that.subjects.find(s => s.type === currentSubject);
                        // 首行为头部
                        subject.cols = items.shift();

                        $.each(items, function (index, val) {
                            that.items[currentSubject].push(val);
                        });
                    }
                });
            }
        },
        'mounted': function() {
            $('#subject-survey-tabs').find('a').on('opened.tabs.amui', function(e) {
                currentSubject = $(this).data('subject');
                let _regions = searchbar.regions();
                app.query(_regions[0] ? _regions[0].id : null);
            });
            let _regions = searchbar.regions();
            this.query(_regions[0] ? _regions[0].id : null);

            // 解决amaze-ui在v-for下自动加am-active的问题
            $('#subject-survey-tabs').find('.am-tabs-nav>li:gt(0)').removeClass('am-active');
            $('#subject-survey-tabs').find('.am-tabs-bd>div:gt(0)').removeClass('am-active');

            // 解决切换tab之后禁止触控操作失效的问题
            $('#subject-survey-tabs').tabs({noSwipe: 1});
        }
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            app.query();
        }
    });
}

$(function () {
    init();
});
