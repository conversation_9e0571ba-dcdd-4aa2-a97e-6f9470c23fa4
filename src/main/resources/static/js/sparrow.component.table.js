Vue.component('component-table', {
    'template': '<div>\
                    <div class="am-g am-margin-bottom-sm">\
                        <button v-if="toolbar.length > 0" @click="_act(toolbar[0])" type="button" class="am-btn am-btn-sm am-btn-primary am-margin-right-sm am-fl">\
                            <span :class="toolbar[0].icon"></span> {{toolbar[0].name}}\
                        </button>\
                        <div ref="dropdown" v-show="toolbar.length > 1" class="am-dropdown">\
                            <button class="am-btn am-btn-sm am-btn-default am-dropdown-toggle">\
                                <span class="mdi-format-list-checkbox"></span>\
                                更多\
                                <span class="mdi-chevron-down"></span>\
                            </button>\
                            <ul class="am-dropdown-content">\
                                <li v-for="(i, index) in toolbar" v-if="index > 0"><a v-text="i.name" @click="_act(i);"></a></li>\
                            </ul>\
                        </div>\
                        <div ref="filter" class="am-fr">\
                            <button type="button" class="am-close">\
                                <span class="mdi-filter-variant am-text-lg"></span>\
                            </button>\
                            <select multiple>\
                                <option v-for="i in columns" :value="i.field" v-text="i.title" :selected="i.hidden === false"></option>\
                            </select>\
                        </div>\
                    </div>\
                    <div class="am-scrollable-horizontal">\
                        <table class="am-table am-table-striped am-table-centered am-table-hover dataTable" style="border-top: 1px solid #ddd">\
                            <thead>\
                                <tr>\
                                    <th v-if="multiple" class="am-text-middle">\
                                        <label class="am-checkbox">\
                                        <input v-model="isAllSelected" @click="_switch();" type="checkbox"/>\
                                        </label>\
                                    </th>\
                                    <th v-for="col in columns" v-if="col.hidden === false" :class="_getHeaderClass(col)" @click="_sort(col);" class="am-text-middle am-text-primary am-text-nowrap">\
                                        <span style="font-size: inherit;">{{col.title}}</span>\
                                    </th>\
                                    <th><a><span class="mdi-menu"></span></a></th>\
                                </tr>\
                            </thead>\
                            <tbody>\
                                <tr v-if="items.length === 0">\
                                    <td :colspan="columns.length + 2" class="am-text-center" style="color: #838FA1">\
                                        <div>\
                                            <span class="mdi-tray-alert am-text-xxxl"></span>\
                                        </div>\
                                        暂无数据\
                                    </td>\
                                </tr>\
                                <tr v-else v-for="row in items" :class="row[\'_ext_\'].class">\
                                    <td v-if="multiple" class="am-text-middle">\
                                        <label class="am-checkbox">\
                                        <input v-model="row[\'_ext_\'].selected" type="checkbox"/>\
                                        </label>\
                                    </td>\
                                    <td v-for="col in columns" v-if="col.hidden === false" :class="col.nowrap ? \'am-text-nowrap\' : \'\'" class="am-text-middle">\
                                        <a v-if="col.control === \'file\'">\
                                            {{$.isArray(row[col.field]) && row[col.field].length > 0 ? (\'共\' + row[col.field].length + \'个文件\') : \'暂无\'}}\
                                        </a>\
                                        <a v-else-if="col.control === \'ranking\'" role="button">\
                                            <span v-for="i in col.validatorValue.options" :class="($.isNumeric(row[col.field]) && row[col.field] >= i) ? \'mdi-star\' : \'mdi-star-outline\'" class="am-icon-sm"></span>\
                                        </a>\
                                        <a v-else-if="$.isFunction(col.click) || $.isFunction(col.mouseover)" @click="$.isFunction(col.click) ? col.click(row, $event) : null" @mouseover="$.isFunction(col.mouseover) ? col.mouseover(row, $event) : null" style="cursor:pointer">{{_getText(col, row)}}</a>\
                                        <span v-else v-html="_getText(col, row)"></span>\
                                    </td>\
                                    <td v-if="isButtonColumn" class="am-text-middle am-hide-sm-only">\
                                        <div class="am-btn-group am-btn-group-xs" style="display: flex; align-items: center">\
                                            <button v-if="btns.length > 0 && btns[0] != null && $.isFunction(btns[0].callback) && (!$.isArray(row[\'_ext_\'].btns) || $.inArray(0, row[\'_ext_\'].btns) != -1)" @click="_save(row);" type="button" class="am-btn am-btn-success" style="margin-left: 5px">\
                                                <span :class="btns[0].icon ? btns[0].icon : \'mdi-square-edit-outline\'"></span> {{btns[0].name ? btns[0].name : \'编辑\'}}\
                                            </button>\
                                            <button v-if="btns.length > 1 && btns[1] != null && $.isFunction(btns[1].callback) && (!$.isArray(row[\'_ext_\'].btns) || $.inArray(1, row[\'_ext_\'].btns) != -1)" @click="_remove(row);" type="button" class="am-btn am-btn-danger" style="margin-left: 5px">\
                                                <span class="mdi-close"></span> 删除\
                                            </button>\
                                            <button v-for="(btn, index) in btns" v-if="index > 1 && btn != null && $.isFunction(btn.callback) && (!$.isArray(row[\'_ext_\'].btns) || $.inArray(index, row[\'_ext_\'].btns) != -1)" @click="btn.callback(row);" type="button" class="am-btn am-btn-default" style="margin-left: 5px">\
                                                <span :class="btn.icon ? btn.icon : \'mdi-menu\'"></span> {{btn.name}}\
                                            </button>\
                                        </div>\
                                    </td>\
                                    <td v-if="isButtonColumn" @click="_switchActionList(row);" class="am-text-middle am-show-sm-only">\
                                        <a class="am-icon-btn am-icon-btn-sm"><span class="mdi-menu-open"></span></a>\
                                    </td>\
                                </tr>\
                            </tbody>\
                        </table>\
                    </div>\
                    <div ref="actionList" class="am-modal-actions">\
                        <div class="am-modal-actions-group">\
                            <ul v-if="chosen !== null" class="am-list">\
                                <li v-if="btns.length > 0 && btns[0] != null && $.isFunction(btns[0].callback) && (!$.isArray(chosen[\'_ext_\'].btns) || $.inArray(0, chosen[\'_ext_\'].btns) != -1)" @click="_switchActionList() &&_save(chosen)">\
                                    <a><span :class="btns[0].icon ? btns[0].icon : \'mdi-square-edit-outline\'"></span> {{btns[0].name ? btns[0].name : \'编辑\'}}</a>\
                                </li>\
                                <li v-if="btns.length > 1 && btns[1] != null && $.isFunction(btns[1].callback) && (!$.isArray(chosen[\'_ext_\'].btns) || $.inArray(1, chosen[\'_ext_\'].btns) != -1)" @click="_switchActionList() && _remove(chosen)" class="am-modal-actions-danger">\
                                    <a><span class="mdi-close"></span> 删除</a>\
                                </li>\
                                <li v-for="(btn, index) in btns" v-if="index > 1 && btn != null && $.isFunction(btn.callback) && (!$.isArray(chosen[\'_ext_\'].btns) || $.inArray(index, chosen[\'_ext_\'].btns) != -1)" @click="_switchActionList() && btn.callback(chosen)">\
                                    <a><span :class="btn.icon ? btn.icon : \'mdi-menu\'"></span> {{btn.name}}</a>\
                                </li>\
                            </ul>\
                        </div>\
                        <div class="am-modal-actions-group">\
                            <button class="am-btn am-btn-secondary am-btn-block" data-am-modal-close>取消</button>\
                        </div>\
                    </div>\
                </div>',
    'props': ['columns', 'multiple', 'sortBy', 'items', 'files', 'btns', 'toolbar', 'message', 'autoInitialize'],
    'data': function () {
        return {
            'chosen': null,
            'filter': []
        };
    },
    'computed': {
        'isAllSelected': function () {
            for (let i = 0; i < this.items.length; i++) {
                if (!this.items[i]['_ext_'].selected)
                    return false;
            }

            return true;
        },
        'isButtonColumn': function () {
            //return $.isFunction(this.create) || $.isFunction(this.save) || $.isFunction(this.remove) || ($.isArray(this.btns) && this.btns.length > 0);
            return $.isFunction(this.save) || $.isFunction(this.remove) || ($.isArray(this.btns) && this.btns.length > 0);
        }
    },
    'methods': {
        '_getHeaderClass': function (col) {
            const that = this;

            if (!col.sortable) {
                return '';
            }

            const _arr = $.grep(that.sortBy, function (val, index) {
                return col.field === val.field;
            });
            if (_arr.length === 0) {
                return 'sorting';
            } else {
                return _arr[0].asc === true ? 'sorting_asc' : 'sorting_desc';
            }
        },
        '_getText': function (col, row) {
            if (col.control === 'datetime' && row[col.field] instanceof String) {
                switch (col.validatorValue.format) {
                    case 'yyyy-MM':
                        return new Date(row[col.field]).format('yyyy-MM');
                    case 'yyyy-MM-dd':
                        return new Date(row[col.field]).format('yyyy-MM-dd');
                    case 'yyyy-MM-dd HH':
                        return new Date(row[col.field]).format('yyyy-MM-dd HH');
                    case 'yyyy-MM-dd HH:mm':
                        return new Date(row[col.field]).format('yyyy-MM-dd HH:mm');
                    default:
                        return new Date(row[col.field]).format('yyyy-MM-dd HH:mm:ss');
                }
            }

            switch (col.control) {
                case 'cascader':
                    const _relativeValue = row[col.validatorValue.relativeField];
                    if (_relativeValue === null) {
                        return null;
                    }

                    if ($.isArray(row.validatorValue.mapper[_relativeValue])) {
                        const _array =$.grep(row.validatorValue.mapper[_relativeValue], function (index, val) {
                            return row[col.field] === val.key;
                        });
                        return _array.length > 0 ? _array[0].value : null;
                    } else {
                        return row.validatorValue.mapper[_relativeValue][row[col.field]];
                    }
                case 'checkbox':
                    if ($.isArray(row[col.field])) {
                        const _array = [];

                        if ($.isArray(col.validatorValue.options)) {
                            for (let i = 0; i < row[col.field].length; i++) {
                                const _temp = $.grep(col.validatorValue.options, function (val) {
                                    return val.key === row[col.field][i];
                                });
                                if (_temp.length > 0) {
                                    _array.push(_temp[0].value);
                                }
                            }
                        } else {
                            for (let i = 0; i < row[col.field].length; i++) {
                                _array.push(col.validatorValue.options[row[col.field][i]]);
                            }
                        }

                        return _array.join(',');
                    } else {
                        return row[col.field];
                    }
                case 'department':
                    if ($.isArray(row[col.field]) && row[col.field].length > 0) {
                        const _names = [];
                        $.each(row[col.field], function (index, val) {
                            _names.push(val.fullName);
                        });

                        return _names.join('，');
                    }

                    return null;
                case 'location':
                    return row[col.field] == null ? null : row[col.field].address;
                case 'radio':
                case 'select':
                    if ($.isArray(col.validatorValue.options)) {
                        const _temp = $.grep(col.validatorValue.options, function (val) {
                            return val.key === row[col.field];
                        });
                        return _temp.length > 0 ? _temp[0].value : '';
                    } else {
                        return col.validatorValue.options[row[col.field]];
                    }
                case 'user':
                    if ($.isArray(row[col.field]) && row[col.field].length > 0) {
                        const _names = [];
                        $.each(row[col.field], function (index, val) {
                            _names.push(val.name);
                        });

                        return _names.join('，');
                    }

                    return null;
                case 'label':
                    if ($.isFunction(col.formatter)) {
                        return col.formatter(row[col.field], row);
                    } else {
                        return row[col.field];
                    }
                default:
                    return row[col.field];
            }
        },
        '_getFiles': function (col, row) {
            if (!$.isArray(row[col.field]) || row[col.field].length === 0) {
                return;
            }

            switch (col.validatorValue.type) {
                case 'image':
                    const _content = $('<div class="am-slider am-slider-default" style="display:none" data-am-flexslider>\
                                        <ul class="am-slides">\
                                            <li></li>\
                                        </ul>\
                                    </div>');
                    $('body').append(_content);

                    ModalUtil.open({
                        'type': 1,
                        'title': false,
                        'area': ['320px', '240px'],
                        'content': _content
                    });

                    _content.flexslider();

                    _content.flexslider('removeSlide', 0);
                    for (let i = 0; i < row[col.field].length; i++) {
                        _content.flexslider('addSlide', '<li><img src="' + row[col.field][i].url + '"/></li>');
                    }
                    break;
                default:
                    break;
            }
        },
        '_switch': function () {
            if (this.isAllSelected) {
                for (let i = 0; i < this.items.length; i++) {
                    Vue.set(this.items[i]['_ext_'], 'selected', false);
                }
            } else {
                for (let i = 0; i < this.items.length; i++) {
                    Vue.set(this.items[i]['_ext_'], 'selected', true);
                }
            }
        },
        '_sort': function (col) {
            const that = this;

            if (!col.sortable) {
                return;
            }

            let _index = -1;
            for (let i = 0; i < that.sortBy.length; i++) {
                if (col.field === that.sortBy[i].field) {
                    _index = i;

                    break;
                }
            }

            if (_index === -1) {
                that.sortBy.push({
                    'field': col.field,
                    'asc': true
                });
            } else {
                if (that.sortBy[_index].asc) {
                    that.sortBy[_index].asc = false;
                } else {
                    that.sortBy.splice(_index, 1);
                }
            }

            that.$emit('query', true);
        },
        '_switchActionList': function (row) {
            if (row == null) {
                $(this.$refs.actionList).modal('close');
            } else {
                this.chosen = row;

                $(this.$refs.actionList).modal('open');
            }

            return true;
        },
        '_save': function (row) {
            this.btns[0].callback(row);
        },
        '_remove': function (row) {
            const that = this;

            ModalUtil.confirm(this.message.deleteConfirmMsg, {
                'title': false,
                'icon': 3
            }, function (index) {
                // 删除
                const _val = that.btns[1].callback(row);
                if (_val instanceof Promise) {
                    _val.then(success => {
                        if (success === true) {
                            // 读取
                            that.$emit('query', false);

                            ModalUtil.close(index);
                        }
                    });
                } else if (_val === true) {
                    // 读取
                    that.$emit('query', false);

                    ModalUtil.close(index);
                }
            });
        },
        '_act': function (action) {
            let _aborted = true;
            if (action.requireSelection === true) {
                for (let i = 0; i < this.items.length; i++) {
                    if (this.items[i]['_ext_'].selected) {
                        _aborted = false;
                        break;
                    }
                }
            } else {
                _aborted = false;
            }

            if (_aborted) {
                ModalUtil.msg('请选择记录', {
                    'title': false,
                    'icon': 0,
                    'time': 2000
                });

                return;
            }

            action.callback();
        }
    },
    'watch': {
        'items': {
            'handler': function (val) {
                this.$nextTick(function () {
                    $(this.$el).find('input[type=checkbox]').uCheck();
                });
            },
            'deep': true
        }
    },
    'mounted': function () {
        const that = this;

        $.each(that.columns, function (index, val) {
            if (val.validator instanceof Promise) {
                const _promise = val.validator;

                switch (val.control) {
                    case 'cascader':
                        val.validatorValue = {
                            'relativeField': '',
                            'mapper': {}
                        };
                        break;
                    case 'checkbox':
                    case 'select':
                    case 'radio':
                    case 'ranking':
                        val.validatorValue = {
                            'options': []
                        };
                        break;
                    case 'datetime':
                        val.validatorValue = {
                            'format': 'yyyy-MM-dd'
                        };
                        break;
                    case 'file':
                        val.validatorValue = {
                            'type': 'file',
                            'multiple': false,
                            'filter': null
                        };
                        break;
                    default:
                        val.validatorValue = {};
                        break;
                }

                _promise.then(data => {
                    $.extend(true, val.validatorValue, data);
                });
            } else {
                val.validatorValue = val.validator;
            }
        });

        // 设置更多按钮
        $(that.$refs.dropdown).dropdown();

        // 设置过滤器
        $(that.$refs.filter).children('select').selected({
            'btnWidth': '100px',
            'btnSize': 'sm'
        }).on('change', function () {
            const _array = [];

            // 获取选中项
            $(that.$refs.filter).find('.am-selected-list > li.am-checked').each(function () {
                _array.push($(this).attr('data-value'));
            });

            // 隐藏非选中项
            $.each(that.columns, function (index, val) {
                val.hidden = $.inArray(val.field, _array) === -1;
            });
        });

        $(that.$refs.filter).children('button').click(function () {
            $(that.$refs.filter).find('.am-selected-btn').trigger('click');
        });

        /*$(that.$refs.filter).next()
            // 修改按钮样式
            .find('button').addClass('am-padding-0').css('border', 'none')
            // 隐藏文本框
            .find('span').hide()
            // 修改图标
            .next().removeClass('am-icon-caret-down').addClass('mdi-filter-variant am-text-primary')

        $(that.$refs.filter).next()
            // 修改字体颜色
            .find('.am-selected-text').css('color', '#333333');*/

        if (that.autoInitialize) {
            that.$emit('query', true);
        }
    }
});

$.fn.extend({
    // 数据表格组件
    'table': function (options) {
        const _options = {
            // 表格定义
            'columns': [],
            // 支持多选
            'multiple': false,
            // 更多按钮
            'toolbar': [],
            // 按钮组
            'btns': [],
            'message': {
                'deleteConfirmMsg': '删除操作不可恢复，请点击“确认”按钮以继续'
            },
            // 目标记录数
            'recordTotal': 0,
            // 返回记录数
            'recordCount': 10,
            // 页数，如果页数为0则不挂载翻页组件
            'indexCount': 5,

            // 读取回调，index从1开始索引
            'query': (recordCount, index, sortBy) => {
                return new Promise(resolve => {
                    resolve(0)
                });
            },

            // 自动初始化
            'autoInitialize': true
        };

        $.extend(true, _options, options);

        const _tableId = Math.random().toString().replace(/\./g, ''),
            _paginationId = Math.random().toString().replace(/\./g, '');
        const _obj = $('<div id="' + _tableId + '">\
                            <component-table :columns="columns" :multiple="multiple" :sort-by="sortBy" :items="items" :files="files" :btns="btns" :toolbar="toolbar" :message="message" :auto-initialize="autoInitialize" @query="query"></component-table>\
                       </div>\
                       <hr class="am-margin-top-0"/>\
                       <div id="' + _paginationId + '"></div>');
        $(this).append(_obj);

        let _table, _pagination;

        // 重置读取回调
        const _call = _options.query;
        _options.query = $.isFunction(_call) ? (recordCount, index) => {
            if (!$.isNumeric(recordCount)) {
                recordCount = _pagination ? (_pagination.getRecordCount() - 0) : _options.recordCount;
            }

            if (!$.isNumeric(index)) {
                index = _pagination ? (_pagination.indexCurrent() - 1) : 0;
            }

            const _val = _call(recordCount, index, _table.getSortBy());
            if (_val instanceof Promise) {
                return new Promise(resolve => {
                    _val.then(data => {
                        _table.setItems(data.items);

                        resolve(data.total);
                    });
                });
            } else {
                _table.setItems(data.items);

                return _val.total;
            }
        } : (recordCount, index) => {
            return 0;
        };

        if (_options.indexCount > 0) {
            _pagination = $(this).find('#' + _paginationId).pagination({
                // 目标记录数
                'recordTotal': _options.recordTotal,
                // 返回记录数
                'recordCount': _options.recordCount,
                // 页数
                'indexCount': _options.indexCount,
                // 读取回调
                'query': _options.query,

                'autoInitialize': false
            });
        }

        $.each(_options.columns, function (index, val) {
            val.hidden = val.hasOwnProperty('hidden') ? val.hidden : false;
        });

        _table = new Vue({
            'el': $(this).find('#' + _tableId)[0],
            'data': {
                'columns': _options.columns,
                'multiple': _options.multiple,
                // 排序字段
                'sortBy': [],
                'items': [],
                'files': [],
                'btns': _options.btns,
                'toolbar': _options.toolbar,
                'message': _options.message,
                'autoInitialize': false
            },
            'methods': {
                'setItems': function (data) {
                    if (!$.isArray(data)) {
                        return;
                    }

                    if (data.length > 0) {
                        for (let i = 0; i < data.length; i++) {
                            if ($.isPlainObject(data[i]['_ext_'])) {
                                data[i]['_ext_'].lately = true;

                                if (!data[i]['_ext_'].hasOwnProperty('selected')) {
                                    data[i]['_ext_'].selected = false;
                                }

                                if (!data[i]['_ext_'].hasOwnProperty('class')) {
                                    data[i]['_ext_'].class = '';
                                }
                            } else {
                                data[i]['_ext_'] = {
                                    'lately': true,
                                    'selected': false,
                                    'class': ''
                                };
                            }
                        }
                    }

                    Vue.set(this, 'items', data);
                },
                'query': function (first) {
                    _pagination.query(first ? 0 : _pagination.getIndexCurrent());
                },
                'getSelected': function () {
                    const _data = [];
                    for (let i = 0; i < this.items.length; i++) {
                        if (this.items[i]['_ext_'].selected) {
                            _data.push(this.items[i]);
                        }
                    }

                    return _data;
                },
                'getSortBy': function () {
                    const _data = [];
                    for (let i = 0; i < this.sortBy.length; i++) {
                        _data.push(this.sortBy[i]);
                    }

                    return _data;
                }
            }
        });

        if (_options.autoInitialize) {
            _pagination.query();
        }

        return {
            'query': _pagination.query,
            'getSelected': _table.getSelected,
            'getSortBy': _table.getSortBy,
            'getIndexCurrent': _pagination.getIndexCurrent,
            'getItemCount': () => {
                return _table.items.length;
            }
        };
    },

    // 增强表格组件
    'ediTable': function (options) {
        const _options = {
            // 表格定义
            'columns': [],
            // 支持多选
            'multiple': false,
            // 按钮组
            'tableBtns': [],
            'toolbar': [],

            // 创建回调
            'create': null,
            // 更新回调
            'save': null,
            // 删除回调
            'remove': null,

            // 目标记录数
            'recordTotal': 0,
            // 返回记录数
            'recordCount': 10,
            // 页数
            'indexCount': 5,
            // 读取回调
            'query': null,

            'autoInitialize': true,

            // 按钮组
            'fileSelected': null,
            'fileDeleted': null,
            'editorBtns': [],

            'message': {
                'deleteConfirmMsg': '删除操作不可恢复，请点击“确认”按钮以继续'
            }
        };

        $.extend(true, _options, options);

        let _table;

        function form(data) {
            const _id = Math.random().toString().replace(/\./g, '');
            let _index;

            // 设置按钮
            const _btns = [];
            _btns.push($.isFunction(_options.save) ? {
                'callback': item => {
                    // 刷新表格
                    const _val = _options.save(item);
                    if (_val instanceof Promise) {
                        _val.then(dto => {
                            if (dto === true || dto.success === true) {
                                ModalUtil.close(_index);

                                if (!dto.hasOwnProperty('scrollToFirstPage') || dto.scrollToFirstPage === true) {
                                    _table.query();
                                } else {
                                    _table.query(_table.getIndexCurrent());
                                }
                            }
                        });
                    } else if (_val === true || _val.success === true) {
                        ModalUtil.close(_index);

                        if (!_val.hasOwnProperty('scrollToFirstPage') || _val.scrollToFirst === true) {
                            _table.query();
                        } else {
                            _table.query(_table.getIndexCurrent());
                        }
                    }
                }
            } : null);
            // 取消操作
            _btns.push(null);
            // 取消删除
            _btns.push(null);

            $.each(_options.editorBtns, function (index, val) {
                _btns.push(val);
            });

            $('body').append('<div id="' + _id + '" class="am-margin"></div>');

            const _columns = [];
            $.each(_options.columns, function (index, val) {
                if (val.hiddenInEditor) {
                    return;
                }

                if (val.hasOwnProperty('classInEditor')) {
                    val.class = val.classInEditor;
                }

                _columns.push(val);
            });

            const _form = $('#' + _id).form({
                // 表单定义
                'rows': _columns,
                'fileSelected': _options.fileSelected,
                'fileDeleted': _options.fileDeleted,
                // 按钮组
                'btns': _btns,
                'message': _options.message
            });

            _form.vue.$nextTick(() => {
                _form.setData(data);
                _form.setReadonly(data === null ? false : !$.isFunction(_options.save));

                let _width = $(window.top).width();
                if (_width < 641) {
                    _width = '90%';
                } else {
                    _width = '60%';
                }

                _index = ModalUtil.open({
                    'type': 1,
                    'title': data === null ? '新增' : '编辑',
                    'content': $('#' + _id),
                    'area': _width,
                    'maxHeight': $(window).height() * 0.9,
                    'end': function () {
                        _form.vue.$destroy();

                        $('#' + _id).remove();
                    }
                });
            });
        }

        const _columns = [];
        $.each(_options.columns, function (index, val) {
            if (!val.hiddenInList) {
                _columns.push(val);
            }
        });

        // 设置按钮
        const _btns = [{
            'name': $.isFunction(_options.save) ? '编辑' : '浏览',
            'icon': $.isFunction(_options.save) ? 'mdi-square-edit-outline' : 'mdi-text-search',
            'callback': data => {
                form(data);
            }
        }];
        _btns.push($.isFunction(_options.remove) ? {
            'callback': _options.remove
        } : null);
        $.each(_options.tableBtns, function (index, val) {
            _btns.push(val);
        });

        // 设置更多按钮
        let _toolbar = [];
        if (_options.create === true) {
            _toolbar.push({
                'name': '新增',
                'icon': 'mdi-plus',
                'callback': () => {
                    form(null);
                }
            })
        }
        if ($.isArray(_options.toolbar)) {
            _toolbar = _toolbar.concat(_options.toolbar);
        }

        _table = $(this).table({
            // 表格定义
            'columns': _columns,
            'multiple': _options.multiple,
            // 源数据
            'items': _options.items,
            // 按钮组
            'btns': _btns,
            'toolbar': _toolbar,
            // 目标记录数
            'recordTotal': _options.recordTotal,
            // 返回记录数
            'recordCount': _options.recordCount,
            // 页数
            'indexCount': _options.indexCount,
            // 读取回调
            'query': _options.query,

            'autoInitialize': false
        });

        if (_options.autoInitialize) {
            _table.query();
        }

        return {
            'query': _table.query,
            'getSelected': _table.getSelected,
            'getSortBy': _table.getSortBy,
            'getIndexCurrent': _table.getIndexCurrent,
            'getItemCount': _table.getItemCount
        };
    }
});
