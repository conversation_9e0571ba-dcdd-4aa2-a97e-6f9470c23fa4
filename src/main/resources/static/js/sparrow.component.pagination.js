Vue.component('component-pagination', {
    'template': '<div class="am-g">\
                    <template v-if="hideOnSinglePage === false || indexes.length > 1">\
                        <span style="line-height: 33px;">合计 <b>{{recordTotal}}</b> 条记录</span>\
                        <div class="am-fr" style="display: flex;align-items: center">\
                            <div class="am-margin-right-sm am-hide-sm-only">\
                                <div class="am-input-group am-input-group-sm">\
                                    <span class="am-input-group-label">每页</span>\
                                    <select v-model="recordCount" type="number" class="am-form-field" style="width: 60px">\
                                        <option value="10">10</option>\
                                        <option value="20">20</option>\
                                        <option value="50">50</option>\
                                        <option value="100">100</option>\
                                    </select>\
                                    <span class="am-input-group-label">条记录</span>\
                                </div>\
                            </div>\
                            <ul class="am-pagination am-margin-0">\
                                <li :class="{\'am-disabled\': !isPrevEnable}" @click="isPrevEnable && _query(Math.ceil(indexCurrent / indexCount - 1) * indexCount - 1);">\
                                    <a class="am-btn am-btn-sm am-margin-bottom-0">\
                                        <i class="mdi-chevron-left"></i>\
                                    </a>\
                                </li>\
                                <li v-for="i in indexes" :class="{\'am-active\': i == indexCurrent\}" @click="_query(i - 1);">\
                                    <a class="am-btn am-btn-sm am-margin-bottom-0">{{i}}</a>\
                                </li>\
                                <li :class="{\'am-disabled\': !isNextEnable}" @click="isNextEnable && _query(Math.ceil(indexCurrent / indexCount - 1) * indexCount + indexCount);">\
                                    <a class="am-btn am-btn-sm am-margin-0">\
                                        <i class="mdi-chevron-right"></i>\
                                    </a>\
                                </li>\
                            </ul>\
                            <div class="am-margin-left-sm am-hide-sm-only">\
                                <div class="am-input-group am-input-group-sm">\
                                    <input v-model="indexInput" type="number" placeholder="页数" class="am-form-field" style="width: 60px">\
                                    <span class="am-input-group-btn">\
                                        <button @click="_query(indexInput - 1);" class="am-btn am-btn-default" type="button">\
                                            <a class="mdi-chevron-right"></a>\
                                        </button>\
                                    </span>\
                                </div>\
                            </div>\
                        </div>\
                    </template>\
                </>',
    'props': ['recordTotal', 'recordCount', 'indexCount', 'indexCurrent', 'autoInitialize', 'hideOnSinglePage'],
    'data': function () {
        return {
            'indexInput': ''
        };
    },
    'computed': {
        // 索引
        'indexes': function () {
            // 清空索引
            const _indexes = [];

            if (this.indexMax <= this.indexCount) {
                for (let i = 1; i <= this.indexMax; i++) {
                    _indexes.push(i);
                }
            } else if (this.indexCurrent <= this.indexCount) {
                for (let i = 1; i <= this.indexCount; i++) {
                    _indexes.push(i);
                }
            } else if (this.indexCurrent > Math.ceil(this.indexMax / this.indexCount - 1) * this.indexCount) {
                for (let i = Math.ceil(this.indexMax / this.indexCount - 1) * this.indexCount + 1; i <= this.indexMax; i++) {
                    _indexes.push(i);
                }
            } else {
                for (let i = Math.ceil(this.indexCurrent / this.indexCount - 1) * this.indexCount + 1; i <= Math.ceil(this.indexCurrent / this.indexCount - 1) * this.indexCount + this.indexCount; i++) {
                    _indexes.push(i);
                }
            }

            return _indexes;
        },
        // 最大索引值
        'indexMax': function () {
            return Math.ceil(this.recordTotal / this.recordCount);
        },
        // 前一页可用
        'isPrevEnable': function () {
            return !(this.indexMax <= this.indexCount || this.indexCurrent <= this.indexCount);
        },
        // 后一页可用
        'isNextEnable': function () {
            return !(this.indexMax <= this.indexCount || (this.indexCurrent > Math.ceil(this.indexMax / this.indexCount - 1) * this.indexCount));
        }
    },
    'methods': {
        '_query': function (index) {
            if (index < 0 || index >= this.indexMax) {
                return;
            }

            // 读取
            this.$emit('query', index, this.recordCount - 0);
        },
    },
    'watch': {
        'recordCount': function (val) {
            // 读取
            this.$emit('query', this.indexCurrent - 1, this.recordCount - 0);
        }
    },
    'mounted': function () {
        if (this.autoInitialize) {
            this._query();
        }
    }
});

$.fn.extend({
    // 翻页组件
    'pagination': function (options) {
        const _options = {
            // 返回记录数
            'recordCount': 10,
            // 页数
            'indexCount': 5,
            // 读取回调，index从0开始索引
            'query': (recordCount, index) => {
                return 0;
            },
            // 自动初始化
            'autoInitialize': true,
            'hideOnSinglePage': true
        };

        $.extend(true, _options, options);

        const _html = '<component-pagination :record-total="recordTotal" :record-count="recordCount" :index-count="indexCount" :index-current="indexCurrent" :auto-initialize="autoInitialize" :hide-on-single-page="hideOnSinglePage" @query="query"></component-pagination>';
        $(this).append(_html);

        const _pagination = new Vue({
            'el': this[0],
            'data': {
                'recordTotal': 0,
                'recordCount': _options.recordCount,
                'indexCount': _options.indexCount,
                'indexCurrent': 1,
                'autoInitialize': _options.autoInitialize,
                'hideOnSinglePage': _options.hideOnSinglePage
            },
            'methods': {
                'setRecordTotal': function (num) {
                    if (!$.isNumeric(num)) {
                        return;
                    }

                    Vue.set(this, 'recordTotal', num);
                },
                'query': function (index, recordCount) {
                    if (!$.isNumeric(index)) {
                        index = 0;
                    }

                    if ($.isNumeric(recordCount)) {
                        this.recordCount = recordCount;
                    }

                    if (!$.isFunction(_options.query)) {
                        return;
                    }

                    // 设置当前索引
                    Vue.set(this, 'indexCurrent', index + 1);

                    const that = this;

                    const _val = _options.query(this.recordCount, index);
                    if (_val instanceof Promise) {
                        _val.then(num => {
                            that.setRecordTotal(num);
                        });
                    } else if ($.isNumeric(_val)) {
                        this.setRecordTotal(_val);
                    }
                },
                'reset': function (num) {
                    // 设置当前索引
                    Vue.set(this, 'indexCurrent', 1);

                    this.setRecordTotal(num);
                }
            }
        });

        return {
            'getRecordCount': () => {
                return _pagination.recordCount;
            },
            'getIndexCurrent': () => {
                return _pagination.$children[0].indexCurrent - 1;
            },
            'setRecordTotal': _pagination.setRecordTotal,
            // 检索
            'query': _pagination.query,
            // 重置
            'reset': _pagination.reset
        }
    }
});