Vue.component('component-searchbar', {
    'template': '<div>\
                    <!-- 大屏显示表单 -->\
                    <div class="am-panel am-panel-default am-hide-sm-only">\
                        <div class="am-panel-bd">\
                            <div class="am-form am-form-horizontal">\
                                <template v-for="(i, index) in layout.rows">\
                                    <div v-show="index === 0 || layout.collapsed === false" :class="{ \'am-margin-bottom-0\': index === 0 && layout.collapsed === true }" class="am-form-group">\
                                        <template v-for="(j, index) in conditions">\
                                            <template v-if="j.row === i">\
                                                <!-- 时间 -->\
                                                <template v-if="j.control === \'datetime\'">\
                                                    <div :id="j.id ? j.id : \'datetime-\' + index" :ref="\'datetime-\' + index" class="am-input-group am-input-group-sm date form_datetime-3 am-u-md-3 am-u-lg-2">\
                                                        <span class="add-on am-input-group-label"><i :class="j.icon ? j.icon : \'mdi-calendar-outline\'" class="icon-th"></i></span>\
                                                        <input :placeholder="j.title" type="text" class="am-form-field" readonly/>\
                                                        <span class="add-on am-input-group-label"><i class="icon-remove mdi-close"></i></span>\
                                                    </div>\
                                                </template>\
                                                <template v-else>\
                                                    <div class="am-input-group am-input-group-sm am-u-md-3 am-u-lg-2">\
                                                        <template>\
                                                            <template v-if="j.control === \'department\'">\
                                                                <span class="am-input-group-label"><i :class="j.icon ? j.icon : \'mdi-sitemap\'"></i></span>\
                                                            </template>\
                                                            <template v-else-if="j.control === \'user\'">\
                                                                <span class="am-input-group-label"><i :class="j.icon ? j.icon : \'mdi-account\'"></i></span>\
                                                            </template>\
                                                            <template v-else>\
                                                                <span class="am-input-group-label"><i :class="j.icon ? j.icon : \'mdi-menu\'"></i></span>\
                                                            </template>\
                                                        </template>\
                                                        <template v-if="j.control === \'cascader\'" >\
                                                            <select v-model="j.val" class="am-form-field">\
                                                                <optgroup :label="\'请选择\' + j.title">\
                                                                    <template v-if="$.isNumeric(j.validatorValue.relativeFieldIndex) && conditions[j.validatorValue.relativeFieldIndex].val !== null">\
                                                                        <template v-if="$.isArray(j.validatorValue.mapper[conditions[j.validatorValue.relativeFieldIndex].val])">\
                                                                            <option v-for="k in j.validatorValue.mapper[conditions[j.validatorValue.relativeFieldIndex].val]" :value="k.key" v-text="k.value"></option>\
                                                                        </template>\
                                                                        <template v-else>\
                                                                            <option v-for="(value, key) in j.validatorValue.mapper[conditions[j.validatorValue.relativeFieldIndex].val]" :value="key" v-text="value"></option>\
                                                                        </template>\
                                                                    </template>\
                                                                </optgroup>\
                                                            </select>\
                                                            <span class="am-input-group-btn">\
                                                                <button @click="_clear(j);" type="button" class="am-btn am-close" style="border: 1px solid;border-left: none;color: rgba(0,0,0,0.2);opacity: unset;">\
                                                                    <span class="mdi-close"></span>\
                                                                </button>\
                                                            </span>\
                                                        </template>\
                                                        <template v-else-if="[\'department\', \'user\'].indexOf(j.control) === -1">\
                                                            <template v-if="j.control === \'number\'">\
                                                                <input v-model="j.val" :placeholder="j.title" type="number" class="am-form-field"/>\
                                                            </template>\
                                                            <template v-else-if="j.control === \'select\'">\
                                                                <select v-model="j.val" class="am-form-field">\
                                                                    <option :value="null" v-text="j.title" disabled selected style="display: none;"></option>\
                                                                    <template v-if="$.isArray(j.validatorValue.options)">\
                                                                        <template v-for="k in j.validatorValue.options">\
                                                                            <option :value="k.key" v-text="k.value"></option>\
                                                                        </template>\
                                                                    </template>\
                                                                    <template v-else>\
                                                                        <template v-for="(value, key) in j.validatorValue.options">\
                                                                            <option :value="key" v-text="value"></option>\
                                                                        </template>\
                                                                    </template>\
                                                                </select>\
                                                            </template>\
                                                            <template v-else>\
                                                                <input v-model="j.val" :placeholder="j.title" type="text" class="am-form-field"/>\
                                                            </template>\
                                                            <span class="am-input-group-btn">\
                                                                <button @click="_clear(j);" type="button" class="am-btn am-close" style="border: 1px solid;border-left: none;color: rgba(0,0,0,0.2);opacity: unset;">\
                                                                    <span class="mdi-close"></span>\
                                                                </button>\
                                                            </span>\
                                                        </template>\
                                                        <template v-else>\
                                                            <template v-if="j.control === \'department\' || j.control === \'user\'">\
                                                                <input :value="_getText(j)" :placeholder="j.title" @click="_getMember(j, j.control);" type="text" class="am-form-field" readonly/>\
                                                                <span class="am-input-group-btn">\
                                                                    <button @click="_clear(j);" type="button" class="am-btn am-btn-default"><i class="mdi-close"></i></button>\
                                                                </span>\
                                                            </template>\
                                                        </template>\
                                                    </div>\
                                                </template>\
                                            </template>\
                                        </template>\
                                        <div class="am-fl">\
                                            <button v-for="(j, index) in btns" v-if="j.row === i" @click="j.callback" class="am-btn am-btn-sm am-btn-primary am-margin-right-sm">\
                                                <span :class="j.icon ? j.icon : \'mdi-menu\'"></span> {{j.title}}\
                                            </button>\
                                        </div>\
                                        <template v-if="layout.rows > 1 && index === 0">\
                                            <div class="am-fr">\
                                                <a @click="_collapse" class="am-close" style="line-height: 33px">\
                                                    <span :class="layout.collapsed ? \'mdi-chevron-down\' : \'mdi-chevron-up\'"></span>\
                                                </a>\
                                            </div>\
                                        </template>\
                                    </div>\
                                </template>\
                            </div>\
                        </div>\
                    </div>\
                    <!-- 小屏显示侧边栏 -->\
                    <a :data-am-offcanvas="offcanvas" class="mdi-magnify am-icon-btn am-primary am-show-sm-only" :style="layout.css.btn" style="z-index: 101" title="搜索"></a>\
                    <div :id="offcanvasId" class="am-offcanvas">\
                        <div class="am-offcanvas-bar am-offcanvas-bar-flip admin-offcanvas-bar">\
                            <div :style="layout.css.bar" class="am-offcanvas-content">\
                                <div class="am-form">\
                                    <template v-for="(i, index) in conditions">\
                                        <div class="am-form-group">\
                                            <!-- 日期 -->\
                                            <template v-if="i.control === \'datetime\'">\
                                                <div :ref="\'datetime-\' + index" class="am-input-group am-input-group-sm date form_datetime-3">\
                                                    <span class="add-on am-input-group-label"><i :class="i.icon ? i.icon : \'mdi-calendar-outline\'" class="icon-th"></i></span>\
                                                    <input :placeholder="i.title" type="text" class="am-form-field" readonly/>\
                                                    <span class="add-on am-input-group-label"><i class="icon-remove mdi-close"></i></span>\
                                                </div>\
                                            </template>\
                                            <template v-else>\
                                                <div class="am-input-group am-input-group-sm">\
                                                    <template>\
                                                        <template v-if="i.control === \'department\'">\
                                                            <span class="am-input-group-label"><i :class="i.icon ? i.icon : \'mdi-sitemap\'"></i></span>\
                                                        </template>\
                                                        <template v-else-if="i.control === \'user\'">\
                                                            <span class="am-input-group-label"><i :class="i.icon ? i.icon : \'mdi-account\'"></i></span>\
                                                        </template>\
                                                        <template v-else>\
                                                            <span class="am-input-group-label"><i :class="i.icon ? i.icon : \'mdi-menu\'"></i></span>\
                                                        </template>\
                                                    </template>\
                                                    <template v-if="i.control === \'cascader\'" >\
                                                        <select v-model="i.val" class="am-form-field">\
                                                            <optgroup :label="\'请选择\' + i.title">\
                                                                <template v-if="$.isNumeric(i.validatorValue.relativeFieldIndex) && conditions[i.validatorValue.relativeFieldIndex].val !== null">\
                                                                    <template v-if="$.isArray(i.validatorValue.mapper[conditions[i.validatorValue.relativeFieldIndex].val])">\
                                                                        <option v-for="j in i.validatorValue.mapper[conditions[i.validatorValue.relativeFieldIndex].val]" :value="j.key" v-text="j.value"></option>\
                                                                    </template>\
                                                                    <template v-else>\
                                                                        <option v-for="(value, key) in i.validatorValue.mapper[conditions[i.validatorValue.relativeFieldIndex].val]" :value="key" v-text="value"></option>\
                                                                    </template>\
                                                                </template>\
                                                            </optgroup>\
                                                        </select>\
                                                        <span class="am-input-group-btn">\
                                                            <button @click="_clear(i);" type="button" class="am-btn am-close" style="border: 1px solid;border-left: none;color: rgba(0,0,0,0.2);opacity: unset;"><i class="mdi-close"></i></button>\
                                                        </span>\
                                                    </template>\
                                                    <template v-else-if="[\'department\', \'user\'].indexOf(i.control) === -1">\
                                                        <template v-if="i.control === \'number\'">\
                                                            <input v-model="i.val" :placeholder="i.title" type="number" class="am-form-field"/>\
                                                        </template>\
                                                        <template v-else-if="i.control === \'select\'">\
                                                            <select v-model="i.val" class="am-form-field">\
                                                                <option :value="null" v-text="i.title" disabled selected style="display: none;"></option>\
                                                                <template v-if="$.isArray(i.validatorValue.options)">\
                                                                    <template v-for="j in i.validatorValue.options">\
                                                                        <option :value="j.key" v-text="j.value"></option>\
                                                                    </template>\
                                                                </template>\
                                                                <template v-else>\
                                                                    <template v-for="(value, key) in i.validatorValue.options">\
                                                                        <option :value="key" v-text="value"></option>\
                                                                    </template>\
                                                                </template>\
                                                            </select>\
                                                        </template>\
                                                        <template v-else>\
                                                            <input v-model="i.val" :placeholder="i.title" type="text" class="am-form-field"/>\
                                                        </template>\
                                                        <span class="am-input-group-btn">\
                                                            <button @click="_clear(i);" type="button" class="am-btn am-close" style="border: 1px solid;border-left: none;color: rgba(0,0,0,0.2);opacity: unset;"><i class="mdi-close"></i></button>\
                                                        </span>\
                                                    </template>\
                                                    <template v-else>\
                                                        <input :value="_getText(i)" :placeholder="i.title" @click="_getMember(i, i.control);" type="text" class="am-form-field" readonly/>\
                                                        <span class="am-input-group-btn">\
                                                            <button @click="_clear(i);" type="button" class="am-btn am-btn-default"><i class="mdi-close"></i></button>\
                                                        </span>\
                                                    </template>\
                                                </div>\
                                            </template>\
                                        </div>\
                                    </template>\
                                    <template v-for="i in btns">\
                                        <div class="am-form-group">\
                                            <button @click="i.callback" :title="i.title" type="button" class="am-btn am-btn-sm am-btn-block am-btn-default">\
                                                <span :class="i.icon ? i.icon : \'mdi-menu\'"></span>\
                                            </button>\
                                        </div>\
                                    </template>\
                                </div>\
                            </div>\
                        </div>\
                    </div>\
                </div>',
    'props': ['layout', 'conditions', 'btns'],
    'data': function () {
        return {
            'offcanvasId': Math.random().toString().replace(/\./g, '')
        };
    },
    'computed': {
        'offcanvas': function () {
            return "{'target': '#" + this.offcanvasId + "'}";
        }
    },
    'methods': {
        '_getText': function (condition) {
            const that = this;

            const _array = [];

            switch (condition.control) {
                case 'cascader':
                    const _relativeValue = that.conditions[condition.validatorValue.relativeFieldIndex];
                    if (_relativeValue === null) {
                        return null;
                    }

                    let _text = null;
                    if ($.isArray(condition.validatorValue.mapper[_relativeValue])) {
                        const _array = $.grep(condition.validatorValue.mapper[_relativeValue], function (index, val) {
                            return condition.val === val.key;
                        });
                        _text = _array.length > 0 ? _array[0].value : null;
                    } else {
                        _text = condition.validatorValue.mapper[_relativeValue][condition.val];
                    }

                    return _text;
                case 'department':
                    $.each(condition.val, function (index, val) {
                        _array.push(val.fullName);
                    });

                    return _array.join(',');
                case 'user':
                    $.each(condition.val, function (index, val) {
                        _array.push(val.name);
                    });

                    return _array.join(',');
                default:
                    return condition.val;
            }
        },
        '_clear': function (condition) {
            switch (condition.control) {
                case 'department':
                case 'user':
                    condition.val.splice(0, condition.val.length);
                    break;
                default:
                    condition.val = null;
                    break;
            }
        },
        '_getMember': function (condition, entity) {
            const _options = {
                'entity': entity
            };

            if ($.isNumeric(condition.validatorValue.max)) {
                _options.max = condition.validatorValue.max
            }

            if (condition.validatorValue.hasOwnProperty('userMapper')) {
                _options.userMapper = condition.validatorValue.userMapper;
            }

            if (condition.validatorValue.hasOwnProperty('deptMapper')) {
                _options.deptMapper = condition.validatorValue.deptMapper;
            }

            if ($.isFunction(condition.validatorValue.queryOrganization)) {
                _options.queryOrganization = condition.validatorValue.queryOrganization;
            }

            if ($.isFunction(condition.validatorValue.queryUsers)) {
                _options.queryUsers = condition.validatorValue.queryUsers;
            }

            switch (entity) {
                case 'department':
                    _options.ok = (departments, users) => {
                        condition.val.splice(0, condition.val.length);
                        $.each(departments, function (index, val) {
                            condition.val.push(val);
                        });

                        return true;
                    };
                    break;
                case 'user':
                    _options.ok = (departments, users) => {
                        condition.val.splice(0, condition.val.length);
                        $.each(users, function (index, val) {
                            condition.val.push(val);
                        });

                        return true;
                    };
                    break;
            }

            Member.get(_options);
        },
        '_collapse': function () {
            this.layout.collapsed = !this.layout.collapsed;
        }
    },
    'mounted': function () {
        const that = this;

        const _promises = [];
        $.each(that.conditions, function (index, val) {
            let _promise;

            if (val.validator instanceof Promise) {
                _promise = val.validator;

                // 设置默认值，避免影响表单的渲染
                switch (val.control) {
                    case 'cascader':
                        val.validatorValue = {
                            'relativeFieldIndex': null,
                            'mapper': {}
                        };
                        break;
                    case 'select':
                        val.validatorValue = {
                            'options': []
                        };
                        break;
                    case 'datetime':
                        val.validatorValue = {
                            'format': 'yyyy-MM-dd'
                        };
                        break;
                }
            } else {
                _promise = new Promise(resolve => {
                    resolve(val.validator);
                });
            }

            _promises.push(new Promise(resolve => {
                _promise.then(data => {
                    // 合并
                    $.extend(true, val.validatorValue, data);

                    resolve(val);
                });
            }));
        });

        Promise.all(_promises).then(conditions => {
            $.each(conditions, function (index, val) {
                // 设置控件
                switch (val.control) {
                    // 设置日期控件
                    case 'datetime':
                        const _options = {
                            'autoclose': true,
                            'language': 'zh-CN',
                            'format': 'yyyy-mm-dd hh:ii:ss',
                            'startView': 'month',
                            'minView': 'hour',
                            'maxView': 'decade'
                        };

                        switch (val.validatorValue.format) {
                            case 'yyyy':
                                _options.format = 'yyyy';
                                _options.startView = 'decade';
                                _options.minView = 'decade';
                                break;
                            case 'yyyy-MM':
                                _options.format = 'yyyy-mm';
                                _options.startView = 'year';
                                _options.minView = 'year';
                                break;
                            case 'yyyy-MM-dd':
                                _options.format = 'yyyy-mm-dd';
                                _options.minView = 'month';
                                break;
                            case 'yyyy-MM-dd HH':
                                _options.format = 'yyyy-mm-dd hh';
                                _options.minView = 'day';
                                break;
                            case 'yyyy-MM-dd HH:mm':
                                _options.format = 'yyyy-mm-dd hh:ii';
                                _options.minView = 'hour';
                                break;
                            case 'yyyy-MM-dd HH:mm:ss':
                                _options.format = 'yyyy-mm-dd hh:ii:ss';
                                _options.minView = 'hour';
                                break;
                        }

                        $(that.$refs['datetime-' + index]).datetimepicker(_options).on('changeDate', function (event) {
                            val.val = event.date === null ? '' : event.date.format(val.validatorValue.format);
                        });

                        if (val.val) {
                            $(that.$refs['datetime-' + index]).datetimepicker('update', val.val);
                        }

                        break;
                }
            });

            // 强制刷新
            that.$forceUpdate();
        });

        // 禁止移动端输入时自动关闭侧边栏
        if (!/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
            $(that.$el).on('open.offcanvas.amui', function () {
                $(window).off('resize.offcanvas.amui orientationchange.offcanvas.amui');
            });
        }
    }
});

$.fn.extend({
    // 搜索栏组件
    'searchbar': function (options) {
        const _options = {
            // 布局
            'layout': {
                'rows': 1,
                'collapsed': true,
                'css': {
                    'bar': 'padding-top: 65px',
                    'btn': 'position: fixed;bottom: 128px;right: 20px'
                }
            },
            // 搜索条件
            'conditions': [],
            // 按钮
            'btns': []
        };

        $.extend(true, _options, options);

        const _html = '<component-searchbar :layout="layout" :conditions="conditions" :btns="btns"></component-searchbar>';
        $(this).append(_html);

        $.each(_options.conditions, function (index, val) {
            // 默认布局
            if (!val.hasOwnProperty('row')) {
                val.row = 1;
            }

            if (!val.hasOwnProperty('val')) {
                if (['department', 'user'].indexOf(val.control) !== -1) {
                    val.val = [];
                } else {
                    val.val = null;
                }
            }

            val.validatorValue = {};
        });

        $.each(_options.btns, function (index, val) {
            // 默认布局
            if (!val.hasOwnProperty('row')) {
                val.row = 1;
            }
        });

        const _search = new Vue({
            'el': this[0],
            'data': {
                'layout': _options.layout,
                'conditions': _options.conditions,
                'btns': _options.btns
            }
        });

        const _component = {};
        $.each(_search.conditions, function (index, val) {
            _component[val.ref ? val.ref : val.title] = () => {
                return _search.conditions[index].val;
            };
        });
        $.each(_search.btns, function (index, val) {
            _component[val.ref ? val.ref : val.name] = _search.btns[index].callback;
        });

        return _component;
    }
});
