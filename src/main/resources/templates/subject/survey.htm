<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<#include "/template-layout-lingling.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <style>
        .layui-layer-content .am-form-group:nth-child(6) {
            max-height: 150px;
            overflow-y: auto;
        }

        .layui-layer-content .am-form-group:nth-child(6) div {
            display: flex;
            flex-wrap: wrap;
            align-items: end;
        }

        .layui-layer-content .am-form-group:nth-child(6) .am-checkbox {
            width: 50%;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/subject/survey.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<@template_layout>
<div class="row">
    <div class="widget am-cf">
        <div class="widget-body am-cf">
            <div id="searchbar"></div>
            <div class="am-panel am-panel-default">
                <div class="am-panel-bd">
                    <div id="app" class="am-scrollable-horizontal">
                        <div class="am-tabs" data-am-tabs="{noSwipe: 1}" data-am-tabs-noswipe="1" id="subject-survey-tabs">
                            <ul class="am-tabs-nav am-nav am-nav-tabs">
                                <li v-for="(subject, idx) in subjects"><a href="javascript: void(0)" :data-subject="subject.type">{{ subject.name }}</a></li>
                            </ul>
                            <div class="am-tabs-bd">
                                <div v-for="(subject, idx) in subjects" class="am-tab-panel am-scrollable-horizontal">
                                    <table width="100%"
                                           class="am-table am-table-striped am-table-centered am-table-hover dataTable">
                                        <thead>
                                        <tr>
                                            <th v-for="col in subject.cols" class="am-text-middle am-text-primary am-text-nowrap">{{ col }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-if="items[subject.type].length === 0">
                                            <td :colspan="subject.cols.length">
                                                <div>
                                                    <span class="mdi-tray-alert am-text-xxxl"></span>
                                                </div>
                                                暂无数据
                                            </td>
                                        </tr>
                                        <tr v-else v-for="i in items[subject.type]">
                                            <td v-for="val in i">{{ val }}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</@template_layout>
</body>
</html>
